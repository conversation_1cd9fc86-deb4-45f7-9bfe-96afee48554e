import numpy as np
import pandas as pd
from sqlalchemy import asc
import streamlit as st

from qnt_research.api import *

st.set_page_config(page_title=None, page_icon=None, layout="wide", initial_sidebar_state="auto", menu_items=None)


def get_last_trade_day() -> pd.Timestamp:
    a = get_trade_days(
        "SHFE",
        ((b := pd.Timestamp.now(tz="Asia/Shanghai")) - pd.Timedelta(days=20)).strftime("%Y-%m-%d"),
        (b + pd.Timedelta(days=20)).strftime("%Y-%m-%d"),
    )
    a = [i.replace(hour=20, minute=35) for i in a]
    return a[np.searchsorted(a, b, "left") - 1]  # type: ignore


@st.cache_data
def get_data(last_trade_day) -> pd.DataFrame:
    t = get_all_futures()
    t = list(zip(t["futures_code"], t["exchange"]))
    t = [i1 + "8888." + i2 for i1, i2 in t if not "-1" in i1]
    res = get_price(
        t,
        "20200101",
        "20991231",
        "1d",
        ["date", "time", "code", "open", "high", "low", "close", "volume", "open_interest"],
    )

    if res is None:
        raise RuntimeError("get_price failed")

    def fnc(series, rolling_num, quantity):
        series = series.rank(ascending=True)
        return np.minimum(
            1,
            np.maximum(
                0,
                (series - series.rolling(rolling_num).quantile(quantity))
                / (series.rolling(rolling_num).quantile(1 - quantity) - series.rolling(rolling_num).quantile(quantity)),
            ),
        )

    for i, j in res.items():
        tmp = get_contract_info(i)
        if tmp is None:
            continue
        j["futures_name"] = tmp["futures_name"]
        j["contract_multiplier"] = tmp["contract_multiplier"]

        j["交易金额"] = j["volume"] * j["close"] * j["contract_multiplier"] / 1e8
        j["沉淀资金"] = j["close"] * j["open_interest"] * j["contract_multiplier"] / 1e8
        j["投机度"] = j["volume"] / j["open_interest"]
        j["合约价值"] = j["close"] * j["contract_multiplier"]

        j["turnover_loc_120"] = fnc(j["交易金额"], 120, 0.10)
        j["turnover_loc_250"] = fnc(j["交易金额"], 250, 0.07)
        j["turnover_loc_500"] = fnc(j["交易金额"], 500, 0.05)

        j["position_amount_loc_120"] = fnc(j["沉淀资金"], 120, 0.10)
        j["position_amount_loc_250"] = fnc(j["沉淀资金"], 250, 0.07)
        j["position_amount_loc_500"] = fnc(j["沉淀资金"], 500, 0.05)

        j["tjd_loc_120"] = fnc(j["投机度"], 120, 0.10)
        j["tjd_loc_250"] = fnc(j["投机度"], 250, 0.07)
        j["tjd_loc_500"] = fnc(j["投机度"], 500, 0.05)

        j["oi_loc_120"] = fnc(j["open_interest"], 120, 0.10)
        j["oi_loc_250"] = fnc(j["open_interest"], 250, 0.07)
        j["oi_loc_500"] = fnc(j["open_interest"], 500, 0.05)

        j["vol_loc_120"] = fnc(j["volume"], 120, 0.10)
        j["vol_loc_250"] = fnc(j["volume"], 250, 0.07)
        j["vol_loc_500"] = fnc(j["volume"], 500, 0.05)

        j["c_loc_120"] = (j["close"] - j["close"].rolling(120).min()) / (
            j["close"].rolling(120).max() - j["close"].rolling(120).min()
        )
        j["c_loc_250"] = (j["close"] - j["close"].rolling(250).min()) / (
            j["close"].rolling(250).max() - j["close"].rolling(250).min()
        )
        j["c_loc_500"] = (j["close"] - j["close"].rolling(500).min()) / (
            j["close"].rolling(500).max() - j["close"].rolling(500).min()
        )

    res0 = [i for _, i in res.items() if not i.empty]
    if len(res0) == 0:
        raise RuntimeError("no data")
    res0 = pd.concat(res0, axis=0)
    res0["date"] = res0["date"].apply(lambda x: str(x))

    res1 = res0.loc[res0["date"] == last_trade_day.strftime("%Y%m%d")].reset_index(drop=True)
    res1["last 120 days"] = res1["code"].apply(lambda x: res[x]["close"].iloc[-120:].tolist())
    return res1


period = st.selectbox("区间统计", ["近半年", "近一年", "近两年"])
tmp = ["date", "code", "futures_name", "交易金额", "沉淀资金", "投机度", "合约价值", "last 120 days"]
if period == "近半年":
    df = get_data(get_last_trade_day())[
        tmp + ["c_loc_120", "vol_loc_120", "oi_loc_120", "turnover_loc_120", "position_amount_loc_120", "tjd_loc_120"]
    ]
    df.rename(
        columns={
            "date": "日期",
            "code": "代码",
            "futures_name": "名称",
            "last 120 days": "近120天",
            "c_loc_120": "价格历史水平",
            "vol_loc_120": "成交量历史水平",
            "oi_loc_120": "持仓量历史水平",
            "turnover_loc_120": "成交额历史水平",
            "position_amount_loc_120": "持仓额历史水平",
            "tjd_loc_120": "投机度历史水平",
        },
        inplace=True,
    )
elif period == "近一年":
    df = get_data(get_last_trade_day())[
        tmp + ["c_loc_250", "vol_loc_250", "oi_loc_250", "turnover_loc_250", "position_amount_loc_250", "tjd_loc_250"]
    ]
    df.rename(
        columns={
            "date": "日期",
            "code": "代码",
            "futures_name": "名称",
            "last 120 days": "近120天",
            "c_loc_250": "价格历史水平",
            "vol_loc_250": "成交量历史水平",
            "oi_loc_250": "持仓量历史水平",
            "turnover_loc_250": "成交额历史水平",
            "position_amount_loc_250": "持仓额历史水平",
            "tjd_loc_250": "投机度历史水平",
        },
        inplace=True,
    )
else:
    df = get_data(get_last_trade_day())[
        tmp + ["c_loc_500", "vol_loc_500", "oi_loc_500", "turnover_loc_500", "position_amount_loc_500", "tjd_loc_500"]
    ]
    df.rename(
        columns={
            "date": "日期",
            "code": "代码",
            "futures_name": "名称",
            "last 120 days": "近120天",
            "c_loc_500": "价格历史水平",
            "vol_loc_500": "成交量历史水平",
            "oi_loc_500": "持仓量历史水平",
            "turnover_loc_500": "成交额历史水平",
            "position_amount_loc_500": "持仓额历史水平",
            "tjd_loc_500": "投机度历史水平",
        },
        inplace=True,
    )
df["代码"] = df["代码"].apply(lambda x: f"*{x}" if len(get_contract_info(x)["night"]) > 0 else x)
df = df.loc[df["交易金额"] > 1]
st.dataframe(
    df,
    height=700,
    use_container_width=True,
    hide_index=True,
    column_config={
        "近120天": st.column_config.LineChartColumn(),
        "成交量": st.column_config.NumberColumn(format="%d"),
        "持仓量": st.column_config.NumberColumn(format="%d"),
        "交易金额": st.column_config.ProgressColumn(
            format="%.2f", min_value=df["交易金额"].min(), max_value=df["交易金额"].max()
        ),
        "沉淀资金": st.column_config.ProgressColumn(
            format="%.2f", min_value=df["沉淀资金"].min(), max_value=df["沉淀资金"].max()
        ),
        "投机度": st.column_config.NumberColumn(format="%.4f"),
        "合约价值": st.column_config.NumberColumn(format="%.2f"),
        "价格历史水平": st.column_config.ProgressColumn(format="%.2f", min_value=0, max_value=1),
        "成交量历史水平": st.column_config.ProgressColumn(format="%.2f", min_value=0, max_value=1),
        "持仓量历史水平": st.column_config.ProgressColumn(format="%.2f", min_value=0, max_value=1),
        "成交额历史水平": st.column_config.ProgressColumn(format="%.2f", min_value=0, max_value=1),
        "持仓额历史水平": st.column_config.ProgressColumn(format="%.2f", min_value=0, max_value=1),
        "投机度历史水平": st.column_config.ProgressColumn(format="%.2f", min_value=0, max_value=1),
    },
)
