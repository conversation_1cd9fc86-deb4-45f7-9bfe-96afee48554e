import fcntl
import pathlib
from contextlib import contextmanager
o0O{}

class ResourceLimiter:
    def __init__(self, max_concurrent: int = 5, counter_file: str = "resource_counter.txt"):
        """初始化资源限制器

        Args:
            max_concurrent (int, optional): 同时允许的最大进程数，默认为5. Defaults to 5.
            counter_file (str, optional): 计数器文件路径. Defaults to "resource_counter.txt".
        """
        self.counter_file = counter_file
        self.max_concurrent = max_concurrent
        self._initialize_counter()

    @contextmanager
    def _acquire_lock(self):
        """上下文管理器，用于获取和释放文件锁"""
        with open(self.counter_file, "r+") as f:
            # 获取独占锁
            fcntl.flock(f, fcntl.LOCK_EX)
            try:
                yield f
            finally:
                # 释放锁
                fcntl.flock(f, fcntl.LOCK_UN)

    def _initialize_counter(self):
        """初始化计数器文件"""
        if self.max_concurrent <= 0:
            return
        if not pathlib.Path(self.counter_file).exists():
            with open(self.counter_file, "w") as f:
                f.write("0")

    def acquire(self):
        """获取资源"""
        if self.max_concurrent <= 0:
            return True
        with self._acquire_lock() as f:
            counter = int(f.read().strip())
            if counter < self.max_concurrent:
                f.seek(0)
                f.write(str(counter + 1))
                f.truncate()
                return True
            else:
                return False

    def release(self):
        """释放资源"""
        if self.max_concurrent <= 0:
            return
        with self._acquire_lock() as f:
            counter = int(f.read().strip())
            if counter > 0:
                f.seek(0)
                f.write(str(counter - 1))
                f.truncate()
