from typing import List

from sb3_contrib import MaskablePPO

from base.calculator import CustomCalculator
from base.data import Data
from base.pool import CustomPool
from env.callback import CustomCallback
from env.environment import CustomEnv
from env.utils import reseed_everything
from utils.argparser import parse_args
from utils.backtest import BackTester


def main(
    name: str,
    index: str,
    start_time: str,
    end_time: str,
    freq: str,
    target: str,
    return_expr: str,
    pool_size: int,
    max_expr_length: int,
    quantile_bars: List[int],
    quantile_ranges: List[float],
    operators: List[str],
    features: List[str],
    constants: List[float],
    bars: List[int],
    steps: int = 2048 * 512,
    device: str = "cuda",
    seed: int = 82,
):
    reseed_everything(seed)
    tokens, return_expr, backward_bars, forward_bars = parse_args(
        operators, features, constants, bars, return_expr, max_expr_length
    )
    backward_bars += max(quantile_bars)
    data = Data(
        index, features, start_time, end_time, freq, backward_bars, forward_bars, step=5, sample_rate=0.2, device=device
    )
    backtester = BackTester()
    calculator = CustomCalculator(data, return_expr, backtester=backtester)
    pool = CustomPool(
        pool_size, calculator, target=target, quantile_bars=quantile_bars, quantile_ranges=quantile_ranges
    )
    env = CustomEnv(pool, tokens, max_expr_length)
    model = MaskablePPO(
        "MlpPolicy",
        env,
        n_steps=128,
        gamma=1,
        ent_coef=0.01,
        batch_size=64,
        learning_rate=5e-5,
        n_epochs=4,
        tensorboard_log="logs",
        device=device,
        verbose=1,
    )
    callback = CustomCallback("out", pool, calculator)
    model.learn(total_timesteps=steps, callback=callback, reset_num_timesteps=False, tb_log_name=name)


if __name__ == "__main__":
    main(
        "default",
        "",
        "",
        "",
        "1d",
        "sortino",
        "DeltaRatio($close, -1)",
        10,
        40,
        [10, 20, 50],
        [0.1, 0.2, 0.3],
        [
            "Neg",
            "Add",
            "RSI",
            "MACD",
            "SMA",
            "EMA",
            "WR",
            "KDJ",
            "ATR",
        ],
        ["OPEN", "CLOSE", "HIGH", "LOW", "TP", "TURNOVER", "VOLUME", "TR"],
        [10, 5, 3, 2, 1, 0.5, 0.1, 0.01],
        [1, 2, 3, 5, 10, 15, 30, 60],
        device="cpu",
    )
