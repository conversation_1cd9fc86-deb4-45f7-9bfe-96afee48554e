from typing import List
import warnings
from base.tokens import PAD_TOKEN, SEP_TOKEN, FeatureToken, OperatorToken, ConstantToken, BarToken
from base.tree import get_all_operators, ExpressionParser


def parse_args(
    operators: List[str], features: List[str], constants: List[float], bars: List[int], expr_str, max_expr_length: int
):
    parser = ExpressionParser()
    expr = parser.parse(expr_str)
    tokens = [PAD_TOKEN, SEP_TOKEN]
    for i, feature in enumerate(features):
        features[i] = feature.lower()
        tokens.append(FeatureToken(feature))
    for operator in operators:
        operator = parser.operators[operator]
        for require_feature in operator.require():
            if require_feature not in features:
                warnings.warn(
                    f"Operator {operator} requires feature {require_feature} which is not in features", UserWarning
                )
        else:
            tokens.append(OperatorToken(operator))
    for constant in constants:
        tokens.append(ConstantToken(float(constant)))
        tokens.append(ConstantToken(float(-constant)))
    for bar in bars:
        if bar < 1:
            continue
        tokens.append(BarToken(int(bar)))
    backward_bars = max_expr_length // 2 * max(bars)
    forward_bars = expr.period.stop
    return tokens, expr, backward_bars, forward_bars
