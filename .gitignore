# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
.VSCodeCounter/
.vscode/
dist/
tools/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]
*.c
test.ipynb

# Log files
*.log
dashboard*

# Package files
*.jar
*.tar
*.tar.*
*.gz

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Data
*.csv
*.pkl
*.png
*.h5

# Settings
Settings.json
bin/quant.tar.gz
dashboard.txt
*checkpoint*

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
tests/free/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

