# -*- coding: utf-8 -*-
# @Description: 每天早上下载因子数据
import sys

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/pro1")

import datetime
import time

import pandas as pd
import smd_module.factor as smdf
import torch
from loguru import logger
from mindgo_api import get_all_trade_days, get_last_trade_day  # type: ignore
from smd_module.utils import StockPoolMask, calc_trade_day, get_latest_trade_day

torch.set_num_threads(6)


try:
    if pd.Timestamp(datetime.date.today()) not in get_all_trade_days():
        sys.exit(0)
    logger.add("/home/<USER>/work/crontabs/因子数据下载.log", backtrace=True, diagnose=True, retention="7 days")

    end_dt = get_latest_trade_day().strftime("%Y-%m-%d")
    last_trade_day = get_last_trade_day().strftime("%Y-%m-%d")
    start_dt = calc_trade_day(end_dt, -10).strftime("%Y-%m-%d")

    # 保存新生产未入库的因子
    logger.info("开始保存【新生产未入库的因子】")
    for hxfactor in smdf.HXFACTOR:
        smdf.dump_hxfactor(hxfactor, None, 0, "2017-01-01", end_dt)
    logger.success("【新生产未入库的因子】保存完成")

    # 已经入库的因子做增量更新
    logger.info("开始【已经入库的因子】增量更新")
    for hxfactor in smdf.HXFACTOR:
        smdf.dump_hxfactor(hxfactor, None, 2, start_dt, end_dt)
    logger.success("【已经入库的因子】增量更新完成")

    # logger.info("开始保存【行业因子】")
    # smdf.dump_industry_factor(start_dt, last_trade_day)
    # logger.success("【行业因子】保存完成")

    logger.info("开始保存【市值因子】")
    smdf.dump_market_cap_factor(start_dt, last_trade_day)
    logger.success("【市值因子】保存完成")

    logger.info("开始保存【资金流】")
    smdf.dump_money_flow_factor(start_dt, last_trade_day)
    logger.success("【资金流】保存完成")

    logger.info("开始保存【财务因子】")
    smdf.dump_financial_factor(start_dt, last_trade_day)
    logger.success("【财务因子】保存完成")

    logger.info("开始更新【StockPoolMask】")
    StockPoolMask(5)
    logger.success("【StockPoolMask】更新完成")

    while pd.Timestamp.now("Asia/Shanghai").time() < datetime.time(9, 8):
        time.sleep(1)
    logger.info("开始保存【次日9点10分后更新的因子】")
    for hxfactor in ["量价强化学习类", "情绪强化学习类"]:
        smdf.dump_hxfactor(hxfactor, None, 2, start_dt, end_dt)
    logger.success("【次日9点10分后更新的因子】保存完成")

    logger.info("最新因子数据下载完成")

except Exception as e:
    logger.opt(exception=e).error(e)
