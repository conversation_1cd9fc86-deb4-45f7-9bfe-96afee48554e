# -*- coding: utf-8 -*-
# @Description: 选股
import sys

sys.path.insert(0, "/home/<USER>/work/lib")
sys.path.insert(0, "/home/<USER>/work/pro")

import datetime
import fcntl
import json
import pathlib
import re
import time

import aichemy.project as yf_prj
import pandas as pd
import torch
from aichemy.data_ops import run_construct_dataset
from aichemy.data_ops.base import check
from aichemy.ml.experiments_idx import ExpTSPrd
from loguru import logger
from mindgo_api import (  # type: ignore
    custom_sector,
    get_all_trade_days,
    get_last_trade_day,
    get_price,
    get_security_info,
    notify_push,
)
from project import *
from smd_module.utils import calc_trade_day, get_latest_trade_day
from sqlalchemy import create_engine

torch.set_num_threads(6)


def write_file(dt, pool, stock_list):
    text = (
        pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        + " "
        + dt
        + " "
        + (pool + ":" + ",".join([f"{i}-{get_security_info(i).display_name}" for i in stock_list]))
    ) + "\n"
    with open("/home/<USER>/work/crontabs/C端荐股.txt", "a") as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        f.write(text)
        fcntl.flock(f, fcntl.LOCK_UN)
    with open("/home/<USER>/share/b2bsm/选股结果.txt", "a") as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        f.write(text)
        fcntl.flock(f, fcntl.LOCK_UN)


try:
    if pd.Timestamp(datetime.date.today()) not in get_all_trade_days():
        sys.exit(0)

    now_str = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    end_dt = get_last_trade_day()
    start_dt = (end_dt - pd.Timedelta(days=128)).strftime("%Y-%m-%d")
    end_dt = end_dt.strftime("%Y-%m-%d")
    end_dt_timestamp = pd.Timestamp(f"{end_dt} 1500", tz="Asia/Shanghai").value

    tmp_t1 = calc_trade_day(end_dt, -500).strftime("%Y-%m-%d")
    tmp = get_price(
        get_all_securities_(tmp_t1, end_dt), tmp_t1, end_dt, "1d", ["close"], fq="post", skip_paused=True, is_panel=True
    )
    close_df = tmp["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    logger.info("数据构建完成")

    while True:
        if re.compile(f"{get_latest_trade_day().strftime('%Y-%m-%d')}.*最新因子数据下载完成").search(
            open("/home/<USER>/work/crontabs/因子数据下载.log", "r").read()
        ):
            break
        time.sleep(1)

    logger.add(
        "/home/<USER>/work/crontabs/C端荐股/log", rotation="00:00", backtrace=True, diagnose=True, retention="5 days"
    )

    pathlib.Path("/home/<USER>/work/crontabs/C端荐股").mkdir(parents=True, exist_ok=True)
    # region 000905选股
    yf_prj.load("/home/<USER>/work/C端项目-板块推荐/成果/000905-model/20250107000000-bmsGNCR0/", globals())
    logger.info(f"加载000905模型 {path}")
    data = run_construct_dataset(pj_construct_dataset, start_dt=start_dt, end_dt=end_dt, t1=t1, t2=t2, **kwargs)
    check(end_dt_timestamp, data)
    model = ExpTSPrd(path, pathlib.Path(path, "args.toml"))
    model.update_args({"device": "cuda"})
    _ = model.load_model()
    result = predict_apply_data(model, data, start_dt, f"{end_dt} 1500", path=path, **kwargs)
    mid_cap_select_stocks = select000905_v6(result, close_df).loc[end_dt_timestamp]
    mid_cap_select_stocks = mid_cap_select_stocks.loc[mid_cap_select_stocks].index.tolist()
    write_file(end_dt, "中证500", mid_cap_select_stocks)
    save_predict_result([result], file=f"/home/<USER>/work/crontabs/C端荐股/{end_dt}_000905_predict_{now_str}.csv")
    logger.info("000905模型预测完成")
    # endregion

    logger.info("选股程序执行完成")

except Exception as e:
    logger.opt(exception=e).error(e)
