# -*- coding: utf-8 -*-
# @Description: 选股
import sys

sys.path.insert(0, "/home/<USER>/work/dev/")
import datetime
import fcntl
import json
import pathlib
import pickle
import re
import threading
import time
import uuid
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import reduce
from itertools import chain
from queue import Queue
from typing import Any, Iterable, Mapping, MutableMapping, Sequence, Tuple

import aichemy.project as yf_prj
import numpy as np
import optuna
import pandas as pd
import smd_module.factor as smdf
import tomli
import torch
from aichemy.data_ops import run_construct_dataset, squeeze_idx_data
from aichemy.data_ops.base import check
from aichemy.factor_analyse import *
from aichemy.factor_analyse.analyse import FactorAnalyse
from aichemy.ml.experiments_idx import *
from aichemy.ml.experiments_idx import ExpTSPrd
from aichemy.utils import from_nstimestamp, slice_dataset, to_nstimestamp
from autogluon.tabular import TabularDataset, TabularPredictor
from IPython.display import display
from loguru import logger
from mindgo_api import (  # type: ignore
    custom_sector,
    get_all_trade_days,
    get_last_tick,
    get_last_trade_day,
    get_price,
    get_security_info,
    get_symbol_industry,
    notify_push,
)
from smd_module.utils import (
    StockPoolMask,
    calc_trade_day,
    get_all_securities_,
    get_index_stocks_periodically,
    get_latest_trade_day,
    get_price_df,
    get_thsindex,
)
from sqlalchemy import create_engine
from tick_trade_api.api import DatafeedHqGenerator, TradeAPI
from tqdm import tqdm

torch.set_num_threads(6)


class DDD:
    def __init__(self, path):
        self.path = path
        if pathlib.Path(path).exists():
            self.predictor = TabularPredictor.load(path)

    def predict(self, x):
        return self.predictor.predict(
            pd.concat([pd.DataFrame(x), pd.Series([0] * len(x)).rename("y")], axis=1), as_pandas=False
        )


def write_file(dt, pool, text):
    text = (pd.Timestamp.now().strftime("%Y%m%d_%H%M%S") + " " + dt + " " + (pool + ":" + text)) + "\n"
    with open("/home/<USER>/work/crontabs_three/turnover.txt", "a") as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        f.write(text)
        fcntl.flock(f, fcntl.LOCK_UN)


def calc_order_quantity_(symbol: str, money: float, price: float) -> int:
    if symbol.startswith("68"):
        if money < price * 200:
            return 0
        else:
            return int(money / price)
    else:
        return int(((money / price) // 100) * 100)


def calc_order_quantity(symbol, price):
    if price < 10:
        money = 12000
    elif price < 15:
        money = 12000
    elif price < 20:
        money = 12000
    elif price < 25:
        money = 12000
    elif price < 30:
        money = 12000
    elif price < 35:
        money = 12000
    elif price < 40:
        money = 12000
    elif price < 45:
        money = 12000
    else:
        money = 12000
    return calc_order_quantity_(symbol, money, price)


def execute_select_stocks() -> Tuple[Sequence[str], Sequence[float], Sequence[int]]:
    end_dt = get_last_trade_day()
    for line in open("/home/<USER>/work/crontabs_three/turnover.txt", "r"):
        if re.compile(f"{end_dt.strftime('%Y-%m-%d')} turnover").search(line):
            logger.info("今日已选股，跳过执行")
            result = [
                [str(i[0]), float(i[1]), int(i[2])]
                for i in re.compile(r"(\d{6}\.\w{2})_[^_]*_(\d+\.?\d*)_(\d*)").findall(line)
            ]
            if result:
                return tuple(list(i) for i in zip(*result))  # type: ignore
            else:
                return [], [], []
    start_dt = (end_dt - pd.Timedelta(days=128)).strftime("%Y-%m-%d")
    end_dt = end_dt.strftime("%Y-%m-%d")
    end_dt_timestamp = pd.Timestamp(f"{end_dt} 1500", tz="Asia/Shanghai").value

    yf_prj.load("/home/<USER>/work/项目四：多因子选股/output/turnover-0608-v1/expire[2025-12-02]/", globals())

    global \
        gl_high_df, \
        gl_low_df, \
        gl_close_df, \
        gl_open_df, \
        gl_turnover_df, \
        gl_turnover_rate_df, \
        gl_hl, \
        gl_return_df, \
        gl_alpha_return_df
    all_securities = get_all_securities_("2018-01-01", end_dt)
    tmp = get_price(
        all_securities,
        "2018-01-01",
        end_dt,
        "1d",
        ["open", "close", "high_limit", "low", "high", "turnover", "turnover_rate"],
        fq="post",
        skip_paused=True,
        is_panel=True,
    )
    gl_high_df = tmp["high"]
    gl_high_df.index = (pd.to_datetime(gl_high_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_low_df = tmp["low"]
    gl_low_df.index = (pd.to_datetime(gl_low_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_close_df = tmp["close"]
    gl_close_df.index = (pd.to_datetime(gl_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_open_df = tmp["open"]
    gl_open_df.index = (pd.to_datetime(gl_open_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_turnover_df = tmp["turnover"]
    gl_turnover_df.index = (pd.to_datetime(gl_turnover_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_turnover_rate_df = tmp["turnover_rate"]
    gl_turnover_rate_df.index = (pd.to_datetime(gl_turnover_rate_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_hl = tmp["low"] < tmp["high_limit"]
    gl_hl.index = (pd.to_datetime(gl_hl.index) + pd.Timedelta(hours=7)).astype(int)

    gl_return_df = gl_close_df.shift(-interval) / gl_open_df.shift(-1) - 1  # type: ignore
    gl_alpha_return_df = gl_return_df.sub(gl_return_df.mean(axis=1), axis=0)
    logger.info(
        f"行情数据获取完成，最新时间为{pd.to_datetime(gl_return_df.index[-1], utc=True).tz_convert('Asia/Shanghai')}。等待因子数据下载完成..."
    )

    while True:
        if re.compile(f"{get_latest_trade_day().strftime('%Y-%m-%d')}.*最新因子数据下载完成").search(
            open("/home/<USER>/work/crontabs/因子数据下载.log", "r").read()
        ):
            break
        time.sleep(1)

    logger.add(
        "/home/<USER>/work/crontabs_three/turnover/log",
        rotation="00:00",
        backtrace=True,
        diagnose=True,
        retention="5 days",
    )

    pathlib.Path("/home/<USER>/work/crontabs_three/turnover").mkdir(parents=True, exist_ok=True)

    # region 000852选股
    data = run_construct_dataset(
        pj_construct_dataset,  # type: ignore
        start_dt=start_dt,
        end_dt=end_dt,
        factor_mapping=factors,  # type: ignore
        **kwargs,  # type: ignore
    )
    check(end_dt_timestamp, data)
    logger.info("数据构建完成")
    model = model = DDD(pathlib.Path(path) / "autogluon_to_deploy")  # type: ignore
    logger.info(f"已从{pathlib.Path(path) / 'autogluon_to_deploy'}加载模型")  # type: ignore
    result, *_ = yf_prj.predict_apply_data(model, data, start_dt, end_dt, path=path, **kwargs, func_pred=lambda x: x[2])  # type: ignore
    logger.info("模型预测完成")

    while datetime.datetime.now().time() < datetime.time(9, 25, 30):
        time.sleep(1)

    logger.info("开始选股...")
    tmp = get_price(
        all_securities, "2024-01-01", "now", "1d", ["open", "close"], fq="pre", skip_paused=True, is_panel=True
    )
    gl_close_df = tmp["close"]
    gl_close_df.index = (pd.to_datetime(gl_close_df.index) + pd.Timedelta(hours=7)).astype(int)
    gl_open_df = tmp["open"]
    gl_open_df.index = (pd.to_datetime(gl_open_df.index) + pd.Timedelta(hours=7)).astype(int)

    stock_mask = gl_open_df.shift(-1) / gl_close_df - 1
    stock_mask = stock_mask.le(0.04) & stock_mask.ge(-0.04)
    stock_mask2 = gl_open_df / gl_close_df.shift(1) - 1
    stock_mask2 = stock_mask2.le(0.04) & stock_mask2.ge(-0.04)

    tmp = gl_open_df.filter(regex="68\d{4}", axis=1).shift(-1).le(35)
    stock_mask1 = gl_open_df.shift(-1).le(70) & tmp.reindex(columns=gl_open_df.columns, fill_value=True)

    result = result[stock_mask][stock_mask2][stock_mask1].loc[end_dt_timestamp]
    industry = {}
    select_stocks = []
    for key, value in result.dropna().sort_values(ascending=False).items():
        ind = get_symbol_industry(key, date=end_dt).s_industryid1
        if industry.get(ind, 0) < 2:
            select_stocks.append(key)
            industry[ind] = industry.get(ind, 0) + 1
            if len(select_stocks) >= 7:
                break
    logger.info("选股完成")

    order_price_lst = [np.round(gl_open_df.shift(-1).loc[end_dt_timestamp].at[s] * 1.01, 2) for s in select_stocks]
    order_quantity_lst = [calc_order_quantity(s, p) for s, p in zip(select_stocks, order_price_lst)]
    write_file(
        dt=end_dt,
        pool="turnover",
        text=",".join(
            [
                f"{i}_{get_security_info(i).display_name}_{j}_{k}"
                for i, j, k in zip(select_stocks, order_price_lst, order_quantity_lst)
            ]
        ),
    )
    notify_push(
        "",
        channel="webhook",
        url="https://3241021-1256001974.ipaas.lite.myqcloud.com/61c9",
        payload={
            "content": json.dumps(
                {
                    "date": end_dt,
                    "symbol_list": select_stocks,
                    "price": order_price_lst,
                    "amount": order_quantity_lst,
                }
            )
        },
    )
    sector_name = "周{}".format(
        {"0": "一", "1": "二", "2": "三", "3": "四", "4": "五"}.get(str(pd.Timestamp.now("Asia/Shanghai").weekday()))
    )
    custom_sector(sector_name, "pop", custom_sector(sector_name).symbol)
    custom_sector(sector_name, "append", select_stocks)
    logger.info("自选板块更新完成")
    # endregion
    logger.info("选股程序执行完成")
    return select_stocks, order_price_lst, order_quantity_lst


class OrderManager:
    today: str
    yestoday: str
    trade_api: TradeAPI
    today_orders: MutableMapping[int, Mapping[str, Any]]
    path: pathlib.Path
    his_orders: pd.DataFrame
    thread_lock: threading.RLock
    thread_event: threading.Event
    order_queue: Queue

    def __init__(self):
        self.today = get_latest_trade_day().strftime("%Y-%m-%d")
        self.yestoday = calc_trade_day(self.today, -1).strftime("%Y-%m-%d")

        # self.trade_api = TradeAPI("69271711")
        self.trade_api = TradeAPI("66301026245")
        self.today_orders = {}

        self.path = pathlib.Path(__file__).parent / "trade.csv"
        if self.path.exists():
            self.his_orders = pd.read_csv(self.path).query('date!="{}"'.format(self.today))
        else:
            self.his_orders = pd.DataFrame()

        self.thread_lock = threading.RLock()
        self.thread_event = threading.Event()
        self.thread_event.set()
        self.order_queue = Queue()
        threading.Thread(target=self._subthread_monitor_orders, daemon=True).start()

    def _subthread_monitor_orders(self):
        logger.info("委托监控线程启动...")
        while self.thread_event.is_set():
            now_time = datetime.datetime.now().time()
            if not reduce(
                lambda x, y: x or y,
                map(
                    lambda x: x[0] <= now_time <= x[1],
                    [(datetime.time(9, 30), datetime.time(11, 30)), (datetime.time(13, 0), datetime.time(14, 57))],
                ),
            ):
                time.sleep(0.5)
                continue
            time.sleep(3)

            with self.thread_lock:
                orders = self.trade_api.get_orders()
            orders = {i["order_id"]: i for i in orders}
            num_open_orders = self.order_queue.qsize()
            for _ in range(num_open_orders):
                order_id, task_id = self.order_queue.get()
                if order_id not in orders:
                    continue
                order = orders[order_id]
                if order["status"] in ["已撤单", "全部成交", "部成部撤", "废单", "内部废单"]:
                    # 如果委托已经完结，并且成交量小于委托量，补充下单
                    # 这里的补单，只针对当前进程内下的单
                    if order["amount"] > order["trade_amount"]:
                        if order["side"] == "BUY":
                            price = get_last_tick([order["symbol"]], ["a1_p"]).at[0, "a1_p"]
                            quantity = order["amount"] - order["trade_amount"]
                            self.buy(order["symbol"], price, quantity, task_id)
                            logger.info(f"补单买入 {order['symbol']} 价格: {price:.2f} 数量: {quantity:.0f}")

                        else:
                            price = get_last_tick([order["symbol"]], ["b1_p"]).at[0, "b1_p"]
                            quantity = order["amount"] - order["trade_amount"]
                            self.sell(order["symbol"], price, quantity, task_id)
                            logger.info(f"补单卖出 {order['symbol']} 价格: {price:.2f} 数量: {quantity:.0f}")
                    else:
                        if order["side"] == "BUY":
                            direction = "买入"
                        else:
                            direction = "卖出"
                        logger.success(
                            f"委托完成: {order_id}, {direction} {order['symbol']} 价格: {order['trade_price']:.2f} 数量: {order['trade_amount']:.0f}"
                        )
                else:
                    # 如果委托未完结，需要把相关信息再次放进队列
                    # 如果已经超过5秒，那么需要撤单再下
                    now = pd.Timestamp.now()
                    if (now - order["datetime"]).total_seconds() > 5:
                        with self.thread_lock:
                            try:
                                self.trade_api.cancel_order(order_id)
                                logger.info(f"撤单成功: {order_id}")
                            except Exception:
                                logger.warning(f"撤单失败: {order_id}")
                    self.order_queue.put((order_id, task_id))
        self.thread_event.set()
        logger.info("委托监控线程退出")

    def get_open_target(
        self, select_stocks: Sequence[str], order_quantity_lst: Sequence[int]
    ) -> Mapping[str, Mapping[str, int]]:
        if not select_stocks:
            return {}

        tmp_task_id = {k: str(uuid.uuid4()) for k in select_stocks}
        tmp_quantity = dict(zip(select_stocks, order_quantity_lst))

        # TODO 需要确保没有待成交
        with self.thread_lock:
            secondary_orders = self.trade_api.secondary_orders()
        for so in [i for i in secondary_orders if i["side"] == "BUY" and i["trade_amount"] > 0]:
            if so["symbol"] in select_stocks:
                tmp_quantity[so["symbol"]] -= so["trade_amount"]
                if tmp_quantity[so["symbol"]] < 0:
                    raise
                self._add_today_orders(
                    {
                        "date": so["datetime"].strftime("%Y-%m-%d"),
                        "time": so["datetime"].strftime("%H:%M:%S"),
                        "symbol": so["symbol"],
                        "direction": "buy",
                        "order_price": so["price"],
                        "order_quantity": so["amount"],
                        "filled_price": so["trade_price"],
                        "filled_quantity": so["trade_amount"],
                        "order_id": "-",
                        "secondary_order_id": so["secondary_order_id"],
                        "task_id": tmp_task_id[so["symbol"]],
                    }
                )

        # TODO: 68开头的股票不下单
        return {k: {tmp_task_id[k]: v} for k, v in tmp_quantity.items() if (v > 0 and not k.startswith("68"))}

    def get_close_target(
        self,
    ) -> Tuple[MutableMapping[str, MutableMapping[str, int]], MutableMapping[str, MutableMapping[str, int]]]:
        close_target_yestoday: MutableMapping[str, MutableMapping[str, int]] = {}
        close_target_the_day_before_yestoday: MutableMapping[str, MutableMapping[str, int]] = {}
        if not self.his_orders.empty:
            for task_id, task_data in self.his_orders.groupby("task_id"):
                if "buy" not in task_data["direction"].tolist():
                    continue
                total_filled_quantity = task_data.groupby("direction")["filled_quantity"].sum()
                open_qty = total_filled_quantity.get("buy", 0)
                close_qty = total_filled_quantity.get("sell", 0)
                date = task_data.loc[task_data["direction"] == "buy", "date"].iat[0]
                symbol = task_data["symbol"].iat[0]
                if open_qty > close_qty:
                    if date == self.yestoday:
                        close_target_yestoday.setdefault(symbol, {}).setdefault(task_id, open_qty - close_qty)  # type: ignore
                    else:
                        close_target_the_day_before_yestoday.setdefault(symbol, {}).setdefault(
                            task_id,  # type: ignore
                            open_qty - close_qty,
                        )

        # TODO 需要确保没有待成交
        close_target = list(close_target_yestoday.keys()) + list(close_target_the_day_before_yestoday.keys())
        with self.thread_lock:
            secondary_orders: Sequence[Mapping[str, Any]] = self.trade_api.secondary_orders()
        tmp: MutableMapping[str, MutableMapping[str, int]] = {}  # {symbol:{secondary_order_id:trade_amount}}
        tmp1 = {}
        for so in [i for i in secondary_orders if i["side"] == "SALE" and i["trade_amount"] > 0]:
            if so["symbol"] in close_target:
                tmp.setdefault(so["symbol"], {}).setdefault(so["secondary_order_id"], so["trade_amount"])
                tmp1[so["secondary_order_id"]] = so

        def gen(a, b):
            if not a:
                yield []
            else:
                a1 = a[1:]
                for bb in b:
                    for r1 in gen(a1, b):
                        yield [bb] + r1

        # task_id: secondary_order_id 1:n
        for symbol, sos in tmp.items():
            flag = False
            for task_ids in gen(
                list(sos.keys()),  # secondary_order_id
                list(close_target_yestoday.get(symbol, {}).keys())
                + list(close_target_the_day_before_yestoday.get(symbol, {}).keys()),  # task_id
            ):
                task_complete = {}
                for secondary_order_id, task_id in zip(list(sos.keys()), task_ids):
                    task_complete[task_id] = task_complete.get(task_id, 0) + sos[secondary_order_id]

                for task_id, close_qty in task_complete.items():
                    if task_id in close_target_yestoday.get(symbol, {}):
                        if close_qty > close_target_yestoday[symbol][task_id]:
                            break
                    elif task_id in close_target_the_day_before_yestoday.get(symbol, {}):
                        if close_qty > close_target_the_day_before_yestoday[symbol][task_id]:
                            break
                else:
                    flag = True

                if flag:
                    for secondary_order_id, task_id in zip(list(sos.keys()), task_ids):
                        self._add_today_orders(
                            {
                                "date": tmp1[secondary_order_id]["datetime"].strftime("%Y-%m-%d"),
                                "time": tmp1[secondary_order_id]["datetime"].strftime("%H:%M:%S"),
                                "symbol": symbol,
                                "direction": "sell",
                                "order_price": tmp1[secondary_order_id]["price"],
                                "order_quantity": tmp1[secondary_order_id]["amount"],
                                "filled_price": tmp1[secondary_order_id]["trade_price"],
                                "filled_quantity": tmp1[secondary_order_id]["trade_amount"],
                                "order_id": "-",
                                "secondary_order_id": secondary_order_id,
                                "task_id": task_id,
                            }
                        )
                    for task_id, close_qty in task_complete.items():
                        if task_id in close_target_yestoday.get(symbol, {}):
                            close_target_yestoday[symbol][task_id] -= close_qty
                            if close_target_yestoday[symbol][task_id] == 0:
                                close_target_yestoday[symbol].pop(task_id)
                            if not close_target_yestoday[symbol]:
                                close_target_yestoday.pop(symbol)
                        else:
                            close_target_the_day_before_yestoday[symbol][task_id] -= close_qty
                            if close_target_the_day_before_yestoday[symbol][task_id] == 0:
                                close_target_the_day_before_yestoday[symbol].pop(task_id)
                            if not close_target_the_day_before_yestoday[symbol]:
                                close_target_the_day_before_yestoday.pop(symbol)
                    break
            else:
                # TODO 如果有多笔开仓一次全平要怎么处理
                raise

        return close_target_yestoday, close_target_the_day_before_yestoday

    def _add_today_orders(self, order: Mapping):
        """不是当前进程下的单也会加入

        Args:
            order (Mapping): 委托
        """
        with self.thread_lock:
            key = len(self.today_orders)
            self.today_orders[key] = order

    def buy(self, symbol: str, price: float, quantity: int, task_id: str):
        now = pd.Timestamp.now()
        if now.time() > datetime.time(15, 1):
            return

        price = np.round(price, 2)
        quantity = int(quantity)
        with self.thread_lock:
            order_id = self.trade_api.order(symbol, quantity, price)
        self.order_queue.put((order_id, task_id))
        self._add_today_orders(
            {
                "date": now.strftime("%Y-%m-%d"),
                "time": now.strftime("%H:%M:%S"),
                "symbol": symbol,
                "direction": "buy",
                "order_price": price,
                "order_quantity": quantity,
                "filled_price": 0.0,
                "filled_quantity": 0,
                "task_id": task_id,
                "order_id": order_id,
                "secondary_order_id": "-",
            }
        )

    def sell(self, symbol: str, price: float, quantity: int, task_id: str):
        now = pd.Timestamp.now()
        if now.time() > datetime.time(15, 1):
            return

        price = np.round(price, 2)
        quantity = int(quantity)
        with self.thread_lock:
            order_id = self.trade_api.order(symbol, -quantity, price)
        self.order_queue.put((order_id, task_id))
        self._add_today_orders(
            {
                "date": now.strftime("%Y-%m-%d"),
                "time": now.strftime("%H:%M:%S"),
                "symbol": symbol,
                "direction": "sell",
                "order_price": price,
                "order_quantity": quantity,
                "filled_price": 0.0,
                "filled_quantity": 0,
                "task_id": task_id,
                "order_id": order_id,
                "secondary_order_id": "-",
            }
        )

    def clear(self):
        """清算，要先结束子线程"""
        self.thread_event.clear()
        self.thread_event.wait()

        today_orders = pd.DataFrame(list(self.today_orders.values()))
        if today_orders.empty:
            return

        # buy和sell下的单只有order_id, 需要补上secondary_order_id
        order_ids = {i: j for i, j in zip(today_orders["order_id"], today_orders.index) if i != "-"}
        for ord in self.trade_api.get_orders():
            if ord["order_id"] in order_ids:
                idx = order_ids[ord["order_id"]]
                today_orders.at[idx, "secondary_order_id"] = ord["secondary_order_id"]

        # 根据secondary_order_id更新成交
        if "-" in today_orders["secondary_order_id"].tolist():
            raise
        order_ids = {i: j for i, j in zip(today_orders["secondary_order_id"], today_orders.index)}
        for ord in self.trade_api.secondary_orders():
            if ord["secondary_order_id"] in order_ids:
                idx = order_ids[ord["secondary_order_id"]]
                today_orders.at[idx, "filled_price"] = ord["trade_price"]
                today_orders.at[idx, "filled_quantity"] = ord["trade_amount"]

        pd.concat([self.his_orders, today_orders], axis=0).to_csv(self.path, index=False)

    def close(self):
        self.trade_api.close()

    def __del__(self):
        self.close()


def execute_trade(select_stocks=None, order_quantity_lst=None):
    select_stocks = select_stocks or []
    order_quantity_lst = order_quantity_lst or []

    order_manager = OrderManager()

    open_target = order_manager.get_open_target(select_stocks, order_quantity_lst)
    if open_target:
        data = get_last_tick(list(open_target.keys()), ["a1_p", "current"]).set_index(
            "id_stock", drop=True, append=False
        )
        for symbol, task in open_target.items():
            for task_id, order_quantity in task.items():
                # 买入当天选出的股票
                order_price = data["current"].at[symbol] * 1.006
                if np.isfinite(order_price):
                    order_manager.buy(symbol, order_price, order_quantity, task_id)
                    logger.info(f"买入 {symbol} 价格: {order_price:.2f} 数量: {order_quantity:.0f}")
        logger.info("当日选股下单完成, 开始监控平仓...")
    else:
        logger.info("当日无需下单买入")

    close_target_yestoday, close_target_the_day_before_yestoday = order_manager.get_close_target()
    close_target = list(close_target_yestoday.keys()) + list(close_target_the_day_before_yestoday.keys())
    logger.info(
        f"昨日之前待平仓: {','.join([f'{k}-{sum(v.values())}' for k, v in close_target_the_day_before_yestoday.items()])}"
    )
    logger.info(f"昨日待平仓: {','.join([f'{k}-{sum(v.values())}' for k, v in close_target_yestoday.items()])}")

    if len(close_target) == 0:
        logger.info("无需平仓, 等待180秒, 任务结束")
        time.sleep(180)
        order_manager.clear()
        order_manager.close()
        return

    dfg = DatafeedHqGenerator(stock=close_target)
    for d in dfg:
        if d["time"] > 145800000:
            dfg.tds.unsub("stock", "USHA")
            dfg.tds.unsub("stock", "USZA")
            break

        symbol = f"{d['code']}.{d['market'][1:3]}"
        if symbol in close_target_yestoday:
            if d["high_price"] == d["uplimit_price"] and d["new_price"] < d["uplimit_price"]:
                for task_id, close_qty in close_target_yestoday[symbol].items():
                    order_manager.sell(symbol, d["bidorder_price"][1], close_qty, task_id)
                    logger.info(f"卖出 {symbol} 价格: {d['bidorder_price'][1]:.2f} 数量: {close_qty:.0f}")
                close_target_yestoday.pop(symbol)

        if symbol in close_target_the_day_before_yestoday:
            if d["new_price"] < d["uplimit_price"]:
                for task_id, close_qty in close_target_the_day_before_yestoday[symbol].items():
                    order_manager.sell(symbol, d["bidorder_price"][1], close_qty, task_id)
                    logger.info(f"卖出 {symbol} 价格: {d['bidorder_price'][1]:.2f} 数量: {close_qty:.0f}")
                close_target_the_day_before_yestoday.pop(symbol)

    logger.info("进入收盘平仓阶段")
    data = get_last_tick(close_target, ["b1_p", "high_limit", "low_limit"])
    b1_p = dict(zip(data["id_stock"], data["b1_p"]))
    hl = dict(zip(data["id_stock"], data["high_limit"]))
    ll = dict(zip(data["id_stock"], data["low_limit"]))

    for symbol, d in chain(close_target_yestoday.items(), close_target_the_day_before_yestoday.items()):
        if b1_p[symbol] < hl[symbol] or not np.isfinite(b1_p[symbol]):
            for task_id, close_qty in d.items():
                order_manager.sell(symbol, ll[symbol], close_qty, task_id)
                logger.info(f"卖出 {symbol} 价格: {ll[symbol]:.2f} 数量: {close_qty:.0f}")

    while datetime.datetime.now().time() < datetime.time(15, 10):
        time.sleep(1)
    order_manager.clear()
    order_manager.close()
    logger.info("任务结束")


if __name__ == "__main__":
    if pd.Timestamp(datetime.date.today()) not in get_all_trade_days():
        sys.exit(0)

    try:
        result = execute_select_stocks()
        execute_trade(select_stocks=result[0], order_quantity_lst=result[2])
    except Exception as e:
        logger.opt(exception=e).error(e)
