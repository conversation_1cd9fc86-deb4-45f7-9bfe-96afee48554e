from typing import Union

import pandas as pd
from loguru import logger

from qnt_utils.toolset import decrypt, to_nstimestamp

from .external_data_handler import ExternalDataHandler
from .hdr import HistoricalDataReq
from .sql_handler import SQLHandlerOfQuotation


class DatabaseManager:
    """负责处理历史行情的提取，会先从本地数据库中取，如果取不到，会向Remote服务请求"""

    def __init__(self, config):
        self._external_data_handler = ExternalDataHandler(config.get("external_data", {}))
        try:
            self._postgres_handler = SQLHandlerOfQuotation(
                username=decrypt(config["database"]["username"]),
                password=decrypt(config["database"]["password"]),
                host=config["database"]["host"],
                port=config["database"]["port"],
            )
        except Exception:
            logger.warning("connect to postgres failed!")
            self._postgres_handler = None

    @staticmethod
    def _formatter(*data: pd.DataFrame) -> pd.DataFrame:
        data_tmp = [i for i in data if not i.empty]
        if data_tmp:
            if len(data_tmp) > 1:
                res = pd.concat(data_tmp, axis=0)
            else:
                res = data_tmp[0]
            res["duplicate"] = res.index
            res.drop_duplicates(subset=["duplicate"], keep="last", inplace=True)
            res.drop("duplicate", axis=1, inplace=True)
            res.sort_index(ascending=True, inplace=True)
            return res
        else:
            return pd.DataFrame()

    def get_data(self, hdr: HistoricalDataReq) -> pd.DataFrame:
        """对外api, 根据请求提取数据, 如果本地数据库中没有, 则从源数据库中提取"""

        def check(
            data: pd.DataFrame, start_time: Union[None, int], end_time: Union[None, int], count: Union[None, int]
        ):
            if data.empty:
                return False

            if start_time is not None and end_time is not None:
                return start_time >= data.index[0] and end_time <= data.index[-1]
            elif start_time is not None and end_time is None:
                return (data.index >= start_time).sum() >= count
            else:
                return (data.index >= end_time).sum() >= count

        if sum([hdr.start_time is None, hdr.end_time is None, hdr.bar_count == 0]) > 1:
            raise ValueError("获取数据时至少需要提供start_time, end_time, count中的2个 ")

        start_time = to_nstimestamp(hdr.start_time) if hdr.start_time is not None else None
        end_time = to_nstimestamp(hdr.end_time) if hdr.end_time is not None else None
        data = []
        wait_update_handlers = []
        for handler in [self._postgres_handler]:
            if handler is not None:
                local = handler.read_sql(hdr)
                data.append(local)
                if check(local, start_time, end_time, hdr.bar_count):
                    break
                else:
                    wait_update_handlers.append(handler)
        else:
            data.extend(self._external_data_handler.get_data(hdr))
        res = self._formatter(*data)
        if res.empty:
            return res
        for i in range(len(wait_update_handlers)):
            wait_update_handlers[i].to_sql(
                hdr.symbol,
                hdr.basic_data,
                res.loc[[j for j in res.index if j not in data[i].index], :],
            )
        # 这里应该不需要了，在底层已经做过转换了
        # if hdr.basic_data == BasicData.MINUTE:
        #     fields_type = {i: j for i, j in BAR_FIELDS.items() if i in res.columns}
        #     res = res.astype(fields_type, errors="ignore")
        if start_time is not None and end_time is not None:
            index_list = list(map(lambda x: start_time <= x <= end_time, res.index))
        elif start_time is not None and end_time is None:
            index_list = res.index[res.index >= start_time].sort_values(ascending=True)[: hdr.bar_count]
        else:
            index_list = res.index[res.index <= end_time].sort_values(ascending=True)[-hdr.bar_count :]

        fields = [i for i in hdr.fields if i in res.columns]
        return res.loc[index_list, fields]
