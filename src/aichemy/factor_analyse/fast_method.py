import numba as nb
import numpy as np
import numpy.typing as npt


@nb.njit(nb.float64[:](nb.float64[:]), cache=True)
def centralize_for_1darray(arr: npt.NDArray[np.float64]) -> npt.NDArray[np.float64]:
    """对数组进行中心化处理，即减去均值

    Args:
        arr (np.ndarray): 输入数组

    Returns:
        np.ndarray: 中心化后的数组
    """
    finites = np.isfinite(arr)
    if finites.astype(np.int32).sum() == 0:
        return arr
    return arr - np.mean(arr[finites])


@nb.njit(nb.float64[:](nb.float64[:]), cache=True)
def nan_to_num_for_1darray(arr: npt.NDArray[np.float64]) -> npt.NDArray[np.float64]:
    """将数组中的无效值（NaN和Inf）替换为有效值

    该函数将输入数组中的NaN替换为均值，将Inf替换为NaN

    Args:
        arr (npt.NDArray[np.float64]): 输入数组

    Returns:
        npt.NDArray[np.float64]: 处理后的数组
    """
    tmp = np.isfinite(arr)
    if tmp.astype(np.int32).sum() == 0:
        return np.full_like(arr, np.nan, dtype=np.float64)

    mean = np.mean(arr[tmp])
    out = np.empty_like(arr, dtype=np.float64)
    for i in range(out.size):
        val = arr[i]
        if np.isnan(val):
            out[i] = mean
        elif np.isinf(val):
            out[i] = np.nan
        else:
            out[i] = val
    return out


@nb.njit(nb.float64[:](nb.float64[:]), cache=True)
def finite_rank_for_1darray(arr: npt.NDArray[np.float64]) -> npt.NDArray[np.float64]:
    """将数组中的有效值进行排序，相等值的排名取平均

    Args:
        arr (np.ndarray): 输入数组

    Returns:
        np.ndarray: 排序后的数组
    """
    n = len(arr)
    sort_index = np.argsort(arr)
    rank_arr = np.arange(1, n + 1, dtype=np.float64)

    idx = 0
    value = arr[sort_index[0]]

    for i in range(1, n):
        if arr[sort_index[i]] > value:
            if i - idx > 1:
                mean_rank = (rank_arr[idx] + rank_arr[i - 1]) / 2
                rank_arr[idx:i] = mean_rank
            idx = i
            value = arr[sort_index[i]]

    if n - idx > 1:
        mean_rank = (rank_arr[idx] + rank_arr[n - 1]) / 2
        rank_arr[idx:] = mean_rank

    result = np.zeros_like(arr, dtype=np.float64)
    result[sort_index] = rank_arr

    return result


@nb.njit(nb.float64(nb.float64[:], nb.float64[:]), cache=True)
def calc_ic(x: npt.NDArray[np.float64], y: npt.NDArray[np.float64]) -> np.float64:
    """计算IC值"""
    if x.size <= 1 or y.size == 1:
        return np.float64(np.nan)
    x_std = x.std()
    if x_std == 0:
        x = np.full_like(x, 0, dtype=np.float64)
    else:
        x = (x - x.mean()) / x_std
    y_std = y.std()
    if y_std == 0:
        y = np.full_like(y, 0, dtype=np.float64)
    else:
        y = (y - y.mean()) / y_std
    return np.corrcoef(x, y)[0, 1]


@nb.njit(nb.float64(nb.float64[:], nb.float64[:]), cache=True)
def calc_normal_ic(x: npt.NDArray[np.float64], y: npt.NDArray[np.float64]) -> np.float64:
    """计算特殊IC值，要求已经normalize"""
    if x.size <= 1 or y.size == 1:
        return np.float64(np.nan)
    return np.mean(x * y) / np.sqrt(np.mean(x**2) * np.mean(y**2))


@nb.njit(nb.float64(nb.float64[:], nb.float64[:]), cache=True)
def calc_rank_ic(x: npt.NDArray[np.float64], y: npt.NDArray[np.float64]) -> np.float64:
    """计算RankIC值"""
    if x.size <= 1 or y.size <= 1:
        return np.float64(np.nan)
    return np.corrcoef(finite_rank_for_1darray(x), finite_rank_for_1darray(y))[0, 1]


@nb.njit(nb.float64[:](nb.int64, nb.float64[:], nb.float64[:], nb.int64, nb.boolean[:]), cache=True)
def divide_groups(
    method: int, x: npt.NDArray[np.float64], y: npt.NDArray[np.float64], num_groups: int, mask: npt.NDArray[np.bool_]
) -> npt.NDArray[np.float64]:
    ret = np.full(num_groups, np.nan, dtype=np.float64)
    if x.size == 0 or y.size == 0:
        return ret

    if num_groups == 1:
        if method == 0:
            ret[0] = calc_ic(x, y)
        elif method == 1:
            ret[0] = calc_rank_ic(x, y)
        elif method == 3:
            ret[0] = y.mean()
        elif method == 4:
            ret[0] = y[mask].mean()
        elif method == 5:
            ret[0] = calc_normal_ic(x, y)
        return ret

    pcts = np.percentile(x, np.arange(num_groups + 1) / num_groups * 100)
    for i, (ub, lb) in enumerate(zip(pcts[:-1], pcts[1:])):
        if i == 0:
            if ub == lb:
                idx = x == ub
            else:
                idx = (ub <= x) & (x <= lb)
        else:
            if ub == lb:
                idx = x == ub
            else:
                idx = (ub < x) & (x <= lb)
        if method == 0:
            ret[i] = calc_ic(x[idx], y[idx])
        elif method == 1:
            ret[i] = calc_rank_ic(x[idx], y[idx])
        elif method == 3:
            y_ = y[idx]
            if y_.size == 0:
                ret[i] = np.nan
            else:
                ret[i] = y_.mean()
        elif method == 4:
            y_ = y[idx & mask]
            if y_.size == 0:
                ret[i] = np.nan
            else:
                ret[i] = y_.mean()
        elif method == 5:
            ret[i] = calc_normal_ic(x[idx], y[idx])

    return ret


@nb.njit(
    nb.float64[:, :, :](
        nb.int64, nb.float64[:, :], nb.float64[:, :], nb.int64, nb.int64, nb.int64, nb.boolean, nb.boolean, nb.int64
    ),
    cache=True,
)
def iter_calc(
    method: int,
    factor_arr: npt.NDArray[np.float64],
    return_arr: npt.NDArray[np.float64],
    sample_ts_length: int,
    sample_cs_length: int,
    sample_ts_step: int,
    is_fillna: bool,
    is_centralize: bool,
    num_groups: int,
) -> npt.NDArray[np.float64]:
    """计算IC值或分组收益

    Args:
        method (int): 计算方法.
            0 - IC，
            1 - RankIC，
            2 - WeightedIC，
            3 - 分组收益统计，
            4 - 分组收益统计（带mask）
            5 - 特殊IC，要求已经normalize
        factor_arr (np.ndarray): 因子数据
        return_arr (np.ndarray): 收益率数据
        sample_ts_length (int): 采样时间长度
        sample_cs_length (int): 采样横截面长度
        sample_ts_step (int): 采样时间步长，传0时取sample_ts_length
        is_fillna (bool): 是否填充NaN
        is_centralize (bool): 是否中心化
        num_groups (int): 分组数量

    Returns:
        np.ndarray: 结果数组, (时序上的采样次数, 截面上的采样次数, 分组数量)
    """
    ts_l, cs_l = factor_arr.shape
    if sample_ts_length == 0:
        sample_ts_length = ts_l
    if sample_cs_length == 0:
        sample_cs_length = cs_l
    if sample_ts_step == 0:
        sample_ts_step = sample_ts_length

    start_lst = np.arange(0, ts_l - sample_ts_length + 1, sample_ts_step, dtype=np.int64)
    l2 = cs_l // sample_cs_length
    ret = np.empty((len(start_lst), l2, num_groups), dtype=np.float64)

    mask = np.arange(sample_ts_length * sample_cs_length) >= ((sample_ts_length - 1) * sample_cs_length)
    for i, start in enumerate(start_lst):
        for j in range(l2):
            end = start + sample_ts_length
            start_ = j * sample_cs_length
            end_ = start_ + sample_cs_length

            x = factor_arr[start:end, start_:end_].flatten()
            if is_fillna:
                x = nan_to_num_for_1darray(x)

            y = return_arr[start:end, start_:end_].flatten()
            if is_centralize:
                y = centralize_for_1darray(y)

            mask_ = np.isfinite(x) & np.isfinite(y)
            ret[i, j] = divide_groups(method, x[mask_], y[mask_], num_groups, mask[mask_])

    return ret


# def _Exc(x, w):
#     return np.sum(w * x)


# def _Cov(x, y, w):
#     return np.sum(w * (x - _Exc(x, w)) * (y - _Exc(y, w)))


# def _Var(x, w):
#     return np.sum(w * x**2) - np.sum(w * x) ** 2


# def _Pearson(x, y, w):
#     return _Cov(x, y, w) / np.sqrt(_Var(x, w) * _Var(y, w))


# def _weight(m, n=10):
#     """返回的结果是权重向量，从小到大"""
#     if not 0 < m <= 1:
#         raise ValueError("m应该在0到1之间")
#     w = np.array([m**i for i in range(n)])[::-1]
#     return 1 / np.sum(w) * w


# @dropna
# def calc_rank_ic(factor_series: Union[pd.Series, np.ndarray], return_series: Union[pd.Series, np.ndarray]):
#     if len(factor_series) > 1 and len(return_series) > 1:
#         factor_series_ = factor_series if isinstance(factor_series, pd.Series) else pd.Series(factor_series)
#         return_series_ = return_series if isinstance(return_series, pd.Series) else pd.Series(return_series)
#         return np.corrcoef(factor_series_.rank(), return_series_.rank())[0, 1]
#     return np.nan


# @dropna
# def calc_ic(factor_series: Union[pd.Series, np.ndarray], return_series: Union[pd.Series, np.ndarray]):
#     if len(factor_series) > 1 and len(return_series) > 1:
#         return np.corrcoef(factor_series, return_series)[0, 1]
#     return np.nan


# @dropna
# def calc_weighted_ic(
#     factor_series: Union[pd.Series, np.ndarray],
#     return_series: Union[pd.Series, np.ndarray],
#     direction: Literal["L", "S"] = "L",
#     weight_decay=1.0,
#     num_groups=10,
# ) -> float:
#     if len(factor_series) > 1 and len(return_series) > 1:
#         ic = calc_ic(factor_series, return_series)
#         weight_series = _weight(weight_decay, n=num_groups)
#         pcts = np.percentile(factor_series, np.arange(0, 100 + 100 / num_groups, 100 / num_groups))
#         weight_matrix = np.full((num_groups, len(factor_series)), np.nan)
#         for i, (ub, lb) in enumerate(zip(pcts[:-1], pcts[1:])):
#             if i == 0:
#                 if ub == lb:
#                     idx = np.where(factor_series == ub)
#                 else:
#                     idx = np.where((ub <= factor_series) & (factor_series <= lb))
#             else:
#                 if ub == lb:
#                     idx = np.where(factor_series == ub)
#                 else:
#                     idx = np.where((ub < factor_series) & (factor_series <= lb))
#             if direction == "L":
#                 if ic > 0:
#                     weight_matrix[i, idx[0]] = weight_series[i] / len(idx[0])
#                 else:
#                     weight_matrix[i, idx[0]] = weight_series[num_groups - 1 - i] / len(idx[0])
#             else:
#                 if ic > 0:
#                     weight_matrix[i, idx[0]] = weight_series[num_groups - 1 - i] / len(idx[0])
#                 else:
#                     weight_matrix[i, idx[0]] = weight_series[i] / len(idx[0])
#         weight = np.nanmean(weight_matrix, axis=0)
#         weight = weight / np.sum(weight)
#         return _Pearson(factor_series, return_series, weight)
#     return np.nan


# def lazy_ic_analyse(
#     factor_df: pd.DataFrame,
#     close_df: pd.DataFrame,
#     interval_length_lst: List[int],
#     sample_shape: List[Tuple[int, int]] = [],
#     method: List[str] = ["IC", "RankIC"],
#     is_fillna: bool = False,
#     plot_kwargs: Optional[Dict] = None,
# ) -> Dict:
#     """有效因子识别方法之一: 单因子IC分析"""
#     ret = {}
#     for interval_length in interval_length_lst:
#         return_df = calc_return(close_df, interval_length, keep_nan=False)
#         for m in method:
#             if plot_kwargs is not None:
#                 plot_kwargs_ = copy.deepcopy(plot_kwargs)
#                 if "title" not in plot_kwargs_:
#                     plot_kwargs_["title"] = f"Return {interval_length}"
#                 else:
#                     plot_kwargs_["title"] = f"{plot_kwargs_['title']} vs Return {interval_length}"
#             else:
#                 plot_kwargs_ = None
#             if m not in ret:
#                 ret[m] = {}
#             tmp = ic_analyse(factor_df, return_df, [(0, 0), (1, 0), (0, 1)] + sample_shape, m, is_fillna, plot_kwargs_)
#             for key, value in tmp.items():
#                 value.pop("ic_arr")
#                 if key not in ret[m]:
#                     ret[m][key] = {}
#                 ret[m][key][interval_length] = value

#     return {k: {kk: pd.DataFrame(vv).T for kk, vv in v.items()} for k, v in ret.items()}


# def ic_analyse(
#     x_df: pd.DataFrame,
#     y_df: pd.DataFrame,
#     sample_shape: List[Tuple[int, int]] = [(0, 0), (1, 0), (0, 1)],
#     method: str = "IC",
#     is_fillna: bool = False,
#     plot_kwargs: Optional[Dict] = None,
# ) -> Dict:
#     """有效因子识别方法之一: 单因子IC分析

#     Args:
#         factor_df (pd.DataFrame): 因子df
#         close_df (pd.DataFrame): 价格df
#         interval_length (int): 调仓周期
#         sample_ts_length (int, optional): 样本时间长度. Defaults to 1.
#         sample_cs_length (int, optional): 样本横截面长度. Defaults to 0.
#         method (str): IC计算方法，支持 "IC", "RankIC", "WeightedIC:L,0.9,10"
#         is_fillna (bool, optional): 是否填充NaN. Defaults to False.
#         plot (bool, optional): 是否绘制IC曲线. Defaults to False.
#         savefig (str, optional): 图片保存路径. Defaults to None.

#     Returns:
#         Dict: IC分析结果, 包括: ic_arr, valid_count, ic_mean, ic_std, ir, abs_ic_mean, abs_ic_std, abs_ic_ir, positive_rate

#     Examples:
#         >>> from aichemy.factor_analyse.analysis import ic_analyse
#         >>> ic_analyse(factor_df, close_df, interval_length=5, is_rank=True)
#     """
#     x_arr, y_arr = align_df(x_df, y_df)

#     ret = {}
#     for sample_ts_length, sample_cs_length in sample_shape:
#         key = f"{sample_ts_length}_{sample_cs_length}"
#         if method == "IC":
#             ret[key] = stat_ic(
#                 iter_calc(0, x_arr, y_arr, sample_ts_length, sample_cs_length, is_fillna, False, 0).squeeze(2)
#             )
#         elif method == "RankIC":
#             ret[key] = stat_ic(
#                 iter_calc(1, x_arr, y_arr, sample_ts_length, sample_cs_length, is_fillna, False, 0).squeeze(2)
#             )

#         if plot_kwargs is not None:
#             plot_kwargs["method"] = method
#             plot_kwargs["sample_shape"] = (sample_ts_length, sample_cs_length)
#             plot_ic(ret[key], plot_kwargs)
#             display(HTML("<hr style='border: 2px solid #ddd;'>"))

#     return ret


# @dropna
# def _calc_group_return(
#     factor_series: pd.Series, return_series: pd.Series, num_groups: int = 3, is_rank: bool = False
# ) -> Dict[str, Any]:
#     """计算分组收益

#     Args:
#         factor_series (pd.Series): 因子序列，index为symbol，value为因子值
#         return_series (pd.Series): 收益序列，index为symbol，value为收益值
#         num_groups (int, optional): 分组数量. Defaults to 3.
#         is_rank (bool, optional): 收益是否基于排名. Defaults to False.

#     Returns:
#         dict: {"return": {1: 0.1, 2: 0.2}, "symbols": {1: ["a", "b"], 2: ["c", "d"]}}
#     """
#     ret = {"return": {i: 0 for i in range(num_groups)}, "symbols": {i: [] for i in range(num_groups)}}
#     if not (len(factor_series) >= num_groups and len(return_series) >= num_groups):
#         return ret

#     # 收益调整，计算超额或计算排名等
#     if is_rank:
#         return_series = return_series.rank()
#         return_series = (return_series - np.nanmean(return_series)) / np.nanstd(return_series)

#     # 分组统计
#     pcts = np.percentile(factor_series, np.arange(0, 100 + 100 / num_groups, 100 / num_groups))
#     for i, (ub, lb) in enumerate(zip(pcts[:-1], pcts[1:])):
#         if i == 0:
#             if ub == lb:
#                 idx = np.where(factor_series == ub)
#             else:
#                 idx = np.where((ub <= factor_series) & (factor_series <= lb))
#         else:
#             if ub == lb:
#                 idx = np.where(factor_series == ub)
#             else:
#                 idx = np.where((ub < factor_series) & (factor_series <= lb))
#         ret["return"][i] = return_series.iloc[idx[0]].mean()
#         ret["symbols"][i] = [i for i in factor_series.index[idx[0]]]
#     return ret


# def stat_gr_df(gr_df, cost, interval_length):
#     def calc(ret_series, cost, interval_length, direction: Optional[Literal["L", "S"]] = None):
#         if direction is None:
#             direction = "L" if ret_series.sum() > 0 else "S"
#         ret = ret_series.sum() / interval_length
#         if direction == "L":
#             ret_series = (ret_series - cost) / interval_length
#         else:
#             ret_series = (-ret_series - cost) / interval_length

#         # 年化收益
#         arr_simple_interest = np.mean(ret_series) * 250
#         # 最终收益
#         cum_ret_series = ret_series.fillna(0).cumsum()
#         final = cum_ret_series.iloc[-1]
#         # 最大回撤
#         max_withdraw = (cum_ret_series.cummax() - cum_ret_series).max()
#         return cum_ret_series, {
#             "RET": ret,
#             "FR": final,
#             "ARR": arr_simple_interest,
#             "MDD": max_withdraw,
#             "D": direction,
#         }

#     def fnc(k):
#         ret = calc(gr_df.iloc[:, k], 0.0, interval_length)[1]
#         tmp = calc(gr_df.iloc[:, k], cost, interval_length)[1]
#         for k, v in tmp.items():
#             if k != "D" and k != "RET":
#                 ret[f"{k}(C: {cost})"] = v
#         return ret

#     num_groups = gr_df.shape[1]
#     r_top_bottom = calc(gr_df.iloc[:, 0] - gr_df.iloc[:, -1], cost, interval_length)

#     if r_top_bottom[1]["D"] == "L":
#         r_long = calc(gr_df.iloc[:, 0], cost, interval_length, "L")
#         r_short = calc(gr_df.iloc[:, -1], cost, interval_length, "S")
#     else:
#         r_long = calc(gr_df.iloc[:, -1], cost, interval_length, "L")
#         r_short = calc(gr_df.iloc[:, 0], cost, interval_length, "S")

#     ret = {
#         "num_groups": num_groups,
#         "group_return_df": gr_df,
#         "group_return": {str(k): fnc(k) for k in range(num_groups)},
#         "ls_portfolio": {"LS": r_top_bottom[1], "L": r_long[1], "S": r_short[1]},
#     }
#     return ret, r_top_bottom, r_long, r_short


# @align_df
# def group_return_analyse(
#     factor_df: pd.DataFrame,
#     close_df: pd.DataFrame,
#     interval_length: int,
#     lookback_period: int = 1,
#     num_groups: int = 3,
#     is_alpha: bool = True,
#     is_rank: bool = False,
#     cost: float = 0.0,
#     plot: bool = False,
#     savefig: Optional[str] = None,
# ) -> Dict:
#     """有效因子识别方法之二: 分组收益分析

#     Args:
#         factor_df (pd.DataFrame): 因子df
#         close_df (pd.DataFrame): 价格df
#         interval_length (int): 调仓周期
#         lookback_period (int, optional): 观察期. Defaults to 1.
#         num_groups (int, optional): 分组数. Defaults to 4.
#         is_alpha (bool, optional): 是否计算超额收益. Defaults to True.
#         is_rank (bool, optional): 是否基于排名计算收益. Defaults to False.
#         cost (float, optional): 交易成本. Defaults to 0.
#         plot (bool, optional): 是否绘制分组收益曲线. Defaults to False.
#         savefig (Optional[str], optional): 图片保存路径. Defaults to None.

#     Raises:
#         ValueError: 因子df和价格df的shape不一致

#     Returns:
#         Dict: ...

#     Examples:
#         >>> from aichemy.factor_analyse.analysis import group_return_analyse
#         >>> group_return_analyse(factor_df, close_df, interval_length=5, num_groups=4, plot=True, is_rank=True)
#     """
#     if factor_df.shape != close_df.shape:
#         raise ValueError(f"factor_df.shape {factor_df.shape} != close_df.shape {close_df.shape}")

#     # 计算分组收益序列
#     return_df = calc_return(close_df, interval_length)
#     # 如果需要计算超额收益，先挑选出因子值非空的，减去平均收益
#     if is_alpha:
#         return_df = return_df.sub(return_df[~factor_df.isna()].mean(axis=1), axis=0)

#     ret_lst = []
#     if lookback_period > 1:
#         turnover_rate = {
#             i: pd.DataFrame(
#                 0.0,
#                 index=factor_df.index,
#                 columns=[f"{i1}_{i2}" for i1 in range(num_groups) for i2 in factor_df.columns],
#             )
#             for i in range(num_groups)
#         }
#     else:
#         turnover_rate = {
#             i: pd.DataFrame(0.0, index=factor_df.index, columns=factor_df.columns) for i in range(num_groups)
#         }
#     for i in range(lookback_period - 1, len(factor_df), 1):
#         if lookback_period > 1:
#             factor_sr = factor_df.iloc[i - lookback_period + 1 : i + 1].reset_index(drop=True).stack()
#             factor_sr.index = factor_sr.index.map(lambda x: f"{x[0]}_{x[1]}")
#             return_sr = return_df.iloc[i - lookback_period + 1 : i + 1].reset_index(drop=True).stack()
#             return_sr.index = return_sr.index.map(lambda x: f"{x[0]}_{x[1]}")
#             ret = _calc_group_return(factor_sr, return_sr, num_groups, is_rank=is_rank)
#         else:
#             ret = _calc_group_return(factor_df.iloc[i], return_df.iloc[i], num_groups, is_rank=is_rank)

#         tmp: Dict[str, Any] = {"dt": from_nstimestamp(factor_df.index[i])}
#         tmp.update(ret)
#         ret_lst.append(tmp)
#         for j in range(num_groups):
#             if len(tmp["symbols"][j]) > 0:
#                 turnover_rate[j].loc[factor_df.index[i], tmp["symbols"][j]] = 1 / len(tmp["symbols"][j])

#     for k in turnover_rate.keys():
#         tmp = turnover_rate[k] - turnover_rate[k].shift(interval_length)
#         turnover_rate[k] = tmp[tmp > 0].sum(axis=1).mean()

#     group_return_df = pd.DataFrame({i["dt"]: i["return"] for i in ret_lst}).T

#     ret, r_top_bottom, r_long, r_short = stat_gr_df(group_return_df, cost, interval_length)
#     ret.update({"symbols": {i["dt"].strftime("%Y-%m-%d %H:%M"): i["symbols"] for i in ret_lst}})

#     for i in range(num_groups):
#         ret["group_return"][str(i)].update({"TR": turnover_rate[i]})

#     if plot:
#         display(HTML("""<span style="font-size: 20px">分组收益</span>"""))

#         # 创建一个图形
#         fig = plt.figure(figsize=(17, 15))
#         # 使用 gridspec 创建子图布局
#         gs = gridspec.GridSpec(3, 1)  # 2 行 2 列

#         # 绘制分组的累计收益曲线
#         ax1 = fig.add_subplot(gs[0, 0])
#         (group_return_df / interval_length).fillna(0).cumsum().plot(ax=ax1)
#         ax1.set_title(f"Group {'Alpha ' if is_alpha else ''}Return(No Cost)")

#         # 绘制多空组合的累计收益曲线
#         ax2 = fig.add_subplot(gs[1, 0])
#         ax2.plot(r_top_bottom[0].index, r_top_bottom[0])
#         ax2.plot(r_top_bottom[0].index, r_long[0], color="red", linestyle="--")
#         ax2.plot(r_top_bottom[0].index, r_short[0], color="green", linestyle="--")

#         # 绘制相关指标
#         ls_ret = ret["ls_portfolio"]
#         x1, x2, y1, y2 = *ax2.get_xlim(), *ax2.get_ylim()
#         ax2.axis([x1, x2 + (x2 - x1) * 0.1, y1, y2])
#         ax2.text(
#             x2,
#             ax2.get_ylim()[0],
#             f"""L-ARR_0: {ls_ret['L']['ARR']:.2%}
# L-MDD_0: {ls_ret['L']['MDD']:.2%}
# ----
# S-ARR_1: {ls_ret['S']['ARR']:.2%}
# S-MDD_1: {ls_ret['S']['MDD']:.2%}
# ----
# ARR: {ls_ret["LS"]['ARR']:.2%}
# MDD: {ls_ret["LS"]['MDD']:.2%}
#             """,
#         )
#         ax2.set_title(f"Long-Short {'Alpha ' if is_alpha else ''}Return(Cost: {cost:.2%})")

#         ax3 = fig.add_subplot(gs[2, 0])
#         ax3.axis("off")
#         df = pd.DataFrame(ret["group_return"]).T
#         # 找到 C 列的最大值的索引
#         max_index = df["RET"].astype("float").idxmax()
#         min_index = df["RET"].astype("float").idxmin()
#         for i in ["RET", "FR", "ARR", "MDD", "TR", f"FR(C: {cost})", f"ARR(C: {cost})", f"MDD(C: {cost})"]:
#             df[i] = df[i].apply(lambda x: "{:.2%}".format(x))

#         # 创建表格
#         table = ax3.table(cellText=df.values, rowLabels=df.index, colLabels=df.columns, cellLoc="center", loc="center")
#         # 遍历每一行，设置背景色
#         for i in range(len(df)):
#             if df.iloc[i]["D"] == "L":
#                 # 设置为浅绿色背景
#                 for j in range(len(df.columns)):
#                     table[(i + 1, j)].set_facecolor("lightcoral")
#             elif df.iloc[i]["D"] == "S":
#                 # 设置为浅红色背景
#                 for j in range(len(df.columns)):
#                     table[(i + 1, j)].set_facecolor("lightgreen")

#             # 设置 C 列最大值那一行的背景色为深红色
#             if str(i) == max_index and df.iloc[i]["D"] == "L":
#                 for j in range(len(df.columns)):
#                     table[(i + 1, j)].set_facecolor("darkred")
#                     table[(i + 1, j)].set_text_props(color="white")  # 设置文本颜色为白色
#             if str(i) == min_index and df.iloc[i]["D"] == "S":
#                 for j in range(len(df.columns)):
#                     table[(i + 1, j)].set_facecolor("darkgreen")
#                     table[(i + 1, j)].set_text_props(color="white")
#         table.auto_set_font_size(False)
#         table.set_fontsize(14)
#         table.scale(1, 1.5)  # 调整表格大小
#         plt.tight_layout()
#         plt.show()

#         if savefig is not None:
#             fig.savefig(savefig if not is_alpha else savefig.replace(".png", "_alpha.png"))

#     return ret


# def ttest(lst: Union[List, np.ndarray, pd.Series]) -> dict:
#     """对回归系数t检验

#     Args:
#         lst (Union[List, np.ndarray, pd.Series]): 每一期单因子线性回归的系数

#     Returns:
#         dict: t检验结果, 包括abs_t_mean, abs_t>2, ttest_t, ttest_p
#     """

#     def convert_to_tvalue(lst):
#         return lst / (np.std(lst) / np.sqrt(len(lst) - 1))

#     lst = np.array([i for i in lst if not pd.isna(i)])
#     if len(lst) <= 1:
#         return {
#             "t_mean": np.nan,
#             "|t|>2 rate": np.nan,
#             "ttest_t": np.nan,
#             "ttest_p": np.nan,
#             "abs_t_mean": np.nan,
#             "abs_t>2": np.nan,
#             "abs_ttest_t": np.nan,
#             "abs_ttest_p": np.nan,
#         }

#     t = convert_to_tvalue(lst)
#     abs_t = convert_to_tvalue(np.abs(lst))

#     return dict(
#         [
#             ("t_mean", np.mean(t)),
#             ("|t|>2 rate", (np.abs(t) > 2).mean() if len(t) > 0 else 0),
#             *zip(["ttest_t", "ttest_p"], ttest_1samp(lst, 0)),
#             ("abs_t_mean", np.mean(abs_t)),
#             ("abs_t>2", (abs_t > 2).mean() if len(abs_t) > 0 else 0),
#             *zip(["abs_ttest_t", "abs_ttest_p"], ttest_1samp(abs_t, 0)),
#         ]
#     )


# # TODO:回归系数t检验支持lookback_period
# @align_df
# def ttest_analyse(
#     factor_df: pd.DataFrame,
#     close_df: pd.DataFrame,
#     public_df_lst: List[pd.DataFrame],
#     interval_length: int,
#     fit_intercept: bool = True,
# ) -> dict:
#     """有效因子识别方法之三: 单因子线性回归和t检验

#     Args:
#         factor_df (pd.DataFrame): 因子df
#         close_df (pd.DataFrame): 价格df
#         public_df_lst (List[pd.DataFrame]): 公共因子df, 主要是行业或市场的指数
#         interval_length (int): 调仓周期
#         fit_intercept (bool, optional): 是否拟合截距项, 如果public_df_lst中添加了市场因子，则可以不需要加截距项. Defaults to True.

#     Raises:
#         ValueError: 因子df和价格df的shape不一致

#     Returns:
#         dict: t检验结果, 包括abs_t_mean, abs_t>2, ttest_t, ttest_p
#     """
#     if factor_df.shape != close_df.shape:
#         raise ValueError(f"factor_df.shape {factor_df.shape} != close_df.shape {close_df.shape}")

#     return_df = (close_df.shift(-interval_length) - close_df) / close_df
#     if pd.isna(return_df).all().all():
#         return ttest([])

#     # TODO 这里可能不太对，不一定都要算收益率
#     public_df_ = []
#     for i in public_df_lst:
#         tmp = (i.shift(-interval_length) - i) / i
#         if pd.isna(tmp).all().all():
#             return ttest([])
#         public_df_.append(tmp)

#     if not pd.isna(factor_df).all().all():
#         std = float(np.nanstd(factor_df))
#         if std == 0 or pd.isna(std):
#             return ttest([])
#         else:
#             factor_df_ = (factor_df - np.nanmean(factor_df)) / std
#         if not pd.isna(factor_df_).all().all():
#             # TODO 是否使用截距项
#             ret = cross_section_linear_regression([factor_df_, *[i for i in public_df_]], return_df, fit_intercept)
#             return ttest(ret.iloc[:, 0])
#         else:
#             return ttest([])
#     else:
#         return ttest([])
