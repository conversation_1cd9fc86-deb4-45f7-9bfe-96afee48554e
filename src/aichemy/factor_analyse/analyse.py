import pathlib
from typing import Callable, Generator, Mapping, Optional, Sequence, Tuple, Union

import matplotlib.ticker
import numpy as np
import numpy.typing as npt
import pandas as pd
import seaborn as sns
from matplotlib import pyplot as plt
from rich import print as rprint
from rich.markdown import Markdown
from tqdm import tqdm

from .fast_method import iter_calc
from .method import group_return_analyse, ic_analyse, ttest_analyse
from .sql_handler import SQLHandlerOfAlphaIC


def full_test(
    factor_df: pd.DataFrame,
    close_df: pd.DataFrame,
    interval_length: int,
    lookback_period: int = 1,
    ic_method: str = "IC",
    num_groups: int = 3,
    is_alpha: bool = True,
    cost: float = 0.0,
    save_path: Optional[str] = None,
    mode: str = "close",
):
    ret = {}
    save_path and pathlib.Path(save_path).mkdir(parents=True, exist_ok=True)
    ret["gr"] = group_return_analyse(
        factor_df,
        close_df,
        interval_length,
        lookback_period=lookback_period,
        num_groups=num_groups,
        is_alpha=is_alpha,
        cost=cost,
        plot=True,
        savefig=None if save_path is None else str(pathlib.Path(save_path, "分组收益.png")),
        mode=mode,
    )
    ret["ic"] = ic_analyse(
        factor_df,
        close_df,
        interval_length,
        lookback_period=lookback_period,
        method=ic_method,
        plot=True,
        savefig=None if save_path is None else str(pathlib.Path(save_path, "IC分析.png")),
        mode=mode,
    )
    # ret["ttest"] = ttest_analyse(factor_df, close_df, [], interval_length=interval_length)
    # display(HTML("""<span style="white-space: pre; font-size: 20px">T检验</span>"""))
    # display(pd.DataFrame([ret["ttest"]]))

    return ret


def stat_method_ic(factor_df, close_df, interval_length, lookback_period) -> pd.Series:
    return ic_analyse(factor_df, close_df, interval_length, lookback_period=lookback_period, method="IC", plot=False)[
        "ic_series"
    ]


def stat_method_rank_ic(factor_df, close_df, interval_length, lookback_period) -> pd.Series:
    return ic_analyse(
        factor_df, close_df, interval_length, lookback_period=lookback_period, method="RankIC", plot=False
    )["ic_series"]


def stat_method_weighted_ic(weighted_ic):
    def fnc(factor_df, close_df, interval_length, lookback_period) -> pd.Series:
        return ic_analyse(
            factor_df,
            close_df,
            interval_length,
            lookback_period=lookback_period,
            method=weighted_ic,
            plot=False,
        )["ic_series"]

    return fnc


def batch_factor_analyse(
    factor_df_gnt: Generator,
    close_df_callable: Callable,
    interval_length: Sequence[int],
    lookback_period: Sequence[int] = [1],
    db="alpha_ic.db",
    stats: Mapping[str, Callable] = {"IC": stat_method_ic, "RankIC": stat_method_rank_ic},
):
    ret = []
    close_df = pd.DataFrame()
    sql_handler = SQLHandlerOfAlphaIC(db)
    fre = "D"
    for label, factor_df in tqdm(factor_df_gnt):
        if (
            close_df.empty
            or factor_df.columns.difference(close_df.columns).size > 0
            or close_df.index[0] > factor_df.index[0]
            or close_df.index[-1] < factor_df.index[-1]
        ):
            close_df, fre = close_df_callable(factor_df)

        for itl in interval_length:
            for lb in lookback_period:
                for stat_name, method in stats.items():
                    stat_value = method(factor_df, close_df, itl, lb)

                    tmp = pd.DataFrame(index=stat_value.index)
                    tmp["timestamp"] = tmp.index
                    tmp["alpha"] = label
                    tmp["interval_length"] = f"{itl}{fre}"
                    tmp["lookback_period"] = lb
                    tmp["stat"] = stat_name
                    tmp["value"] = stat_value

                    ret.append(tmp)
                    sql_handler.insert(tmp.dropna())

    return pd.concat(ret, axis=0)


# -------------------------------------fast version-------------------------------------


def align_df(
    factor_df: pd.DataFrame, return_df: pd.DataFrame
) -> Tuple[npt.NDArray[np.float64], npt.NDArray[np.float64], pd.DatetimeIndex]:
    """将因子数据和收益率数据对齐到相同的时间区间

    Args:
        factor_df (pd.DataFrame): 因子数据
        return_df (pd.DataFrame): 收益率数据

    Returns:
        Tuple[npt.NDArray[np.float64], npt.NDArray[np.float64], pd.DatetimeIndex]: 因子数据,收益率数据和时间索引
    """
    start: int = max(factor_df.index.min(), return_df.index.min())
    end: int = min(factor_df.index.max(), return_df.index.max())
    f_idx: pd.DatetimeIndex = factor_df.index[(factor_df.index >= start) & (factor_df.index <= end)]  # type: ignore
    c_idx: pd.DatetimeIndex = return_df.index[(return_df.index >= start) & (return_df.index <= end)]  # type: ignore
    idx: pd.DatetimeIndex = f_idx.union(c_idx).unique().sort_values(ascending=True)
    # TODO
    try:
        if idx.diff().min() < 24 * 3600 * 1e9:  # type: ignore
            pass
    except AttributeError:
        pass
    col: pd.Index = factor_df.columns.intersection(return_df.columns)
    return (factor_df.reindex(index=idx, columns=col).values, return_df.reindex(index=idx, columns=col).values, idx)


class FactorAnalyseResult:
    array: npt.NDArray[np.float64]
    method: str
    sample_ts_length: int
    sample_cs_length: int
    sample_ts_step: int
    is_fillna: bool
    num_groups: int
    dtt_idx: pd.DatetimeIndex

    def __init__(
        self,
        array: npt.NDArray[np.float64],
        method: str,
        sample_ts_length: int,
        sample_cs_length: int,
        sample_ts_step: int,
        is_fillna: bool,
        num_groups: int,
        dtt_idx: pd.DatetimeIndex,
    ):
        self.array = array
        self.method = method
        self.sample_ts_length = sample_ts_length
        self.sample_cs_length = sample_cs_length
        self.sample_ts_step = sample_ts_step
        self.is_fillna = is_fillna
        self.num_groups = num_groups
        self.dtt_idx = dtt_idx

    def group(self, idx: Sequence[int]):
        """取出指定组的结果"""
        idx = [int(i) if i >= 0 else self.num_groups + i for i in idx]
        if max(idx) >= self.num_groups:
            raise ValueError(f"group index {idx} out of range, num_groups: {self.num_groups}")
        return self.__class__(
            array=self.array[:, :, idx],
            method=self.method,
            sample_ts_length=self.sample_ts_length,
            sample_cs_length=self.sample_cs_length,
            sample_ts_step=self.sample_ts_step,
            is_fillna=self.is_fillna,
            num_groups=len(idx),
            dtt_idx=self.dtt_idx,
        )

    @property
    def sample_shape(self) -> Tuple[int, int]:
        return self.sample_ts_length, self.sample_cs_length

    @property
    def stats(self) -> pd.DataFrame: ...
    def plot(self, **kwargs):
        name = "_".join(
            [
                self.method,
                "sample" + str((self.sample_ts_length, self.sample_cs_length, self.sample_ts_step)),
            ]
        ).replace(" ", "")
        if "title" in kwargs:
            name = kwargs["title"] + "_" + name
        rprint(Markdown(f"# {name} 分析"))
        nrow = (self.sample_ts_length != 0) + (self.sample_cs_length != 0) + 1
        figure, ax = plt.subplots(nrows=nrow, ncols=1, figsize=(13, 4.5 * nrow))

        # 第一个图，绘制IC累积曲线，展示IC在时序上的累积情况
        if self.sample_ts_length != 0:
            if nrow == 1:
                ax1 = ax
            else:
                ax1 = ax[0]
            tmp = pd.DataFrame(np.nanmean(self.array, axis=1)).fillna(0).cumsum(axis=0).reset_index(drop=True)
            if "groups" in kwargs:
                tmp = tmp[kwargs["groups"]]
            sns.lineplot(tmp, ax=ax1)
            ax1.set_title(f"{name} Time Series Cumsum")
            ax1.legend(tmp.columns.tolist(), ncol=np.ceil(self.num_groups / 5))

        # 第二个图，绘制IC截面上的频数分布
        if self.sample_cs_length != 0:
            if nrow == 1:
                ax2 = ax
            else:
                ax2 = ax[1]
            ic_cs_mean = pd.DataFrame(np.nanmean(self.array, axis=0))
            if "groups" in kwargs:
                ic_cs_mean = ic_cs_mean[kwargs["groups"]]
            sns.kdeplot(ic_cs_mean, ax=ax2)
            ax2.set_title(f"{name} Cross Section Density")
            ax2.legend(ic_cs_mean.columns.tolist(), ncol=np.ceil(self.num_groups / 5))

        df: pd.DataFrame = self.stats
        for col in df.columns:
            if col in ["count", "valid_count"]:
                df[col] = df[col].map(lambda x: f"{x:.0f}")
            else:
                df[col] = df[col].map(lambda x: f"{x:.4f}")
        table = ax[-1].table(
            cellText=df.values, rowLabels=df.index, colLabels=df.columns, cellLoc="center", loc="center"
        )
        table.auto_set_font_size(False)
        table.set_fontsize(14)
        table.scale(1, 1.5)  # 调整表格大小
        ax[-1].axis("off")
        plt.tight_layout()

        plt.show()
        if kwargs.get("savefig", False):
            pathlib.Path(kwargs["savefig"]).mkdir(parents=True, exist_ok=True)
            figure.savefig(pathlib.Path(kwargs["savefig"], name))


class ICResult(FactorAnalyseResult):
    def __init__(
        self,
        array: npt.NDArray[np.float64],
        method: str,
        sample_ts_length: int,
        sample_cs_length: int,
        sample_ts_step: int,
        is_fillna: bool,
        num_groups: int,
        dtt_idx: pd.DatetimeIndex,
    ):
        super().__init__(
            array, method, sample_ts_length, sample_cs_length, sample_ts_step, is_fillna, num_groups, dtt_idx
        )

    @property
    def stats(self) -> pd.DataFrame:
        if self.num_groups == 1:
            ic_arr = self.array.flatten()
            ic_arr_ = ic_arr[np.isfinite(ic_arr)]
            if ic_arr_.size == 0:
                ret = {
                    "valid_count": 0,
                    "ic_mean": np.nan,
                    "ic_std": np.nan,
                    "ir": np.nan,
                    "positive_rate": np.nan,
                    "abs_ic_mean": np.nan,
                    "abs_ic_std": np.nan,
                    "abs_ic_ir": np.nan,
                }
            elif ic_arr_.size == 1:
                ret = {
                    "valid_count": 1,
                    "ic_mean": ic_arr[0],
                    "ic_std": np.nan,
                    "ir": np.nan,
                    "positive_rate": np.nan,
                    "abs_ic_mean": np.abs(ic_arr[0]),
                    "abs_ic_std": np.nan,
                    "abs_ic_ir": np.nan,
                }
            else:
                abs_ic_arr_ = np.abs(ic_arr_)
                ret = {
                    "valid_count": ic_arr_.size,
                    "ic_mean": (t1 := np.mean(ic_arr_)),
                    "ic_std": (t2 := np.std(ic_arr_)),
                    "ir": abs(t1) / t2,
                    "positive_rate": np.sum(ic_arr_ > 0) / ic_arr_.size,
                    "abs_ic_mean": (t3 := np.mean(abs_ic_arr_)),
                    "abs_ic_std": (t4 := np.std(abs_ic_arr_)),
                    "abs_ic_ir": t3 / t4,
                }
            return pd.DataFrame({0: ret}).T
        return pd.concat([self.group([i]).stats.iloc[0].rename(i) for i in range(self.num_groups)], axis=1).T


class GroupReturnResult(FactorAnalyseResult):
    is_alpha: bool

    def __init__(
        self,
        array: npt.NDArray[np.float64],
        sample_ts_length: int,
        sample_cs_length: int,
        sample_ts_step: int,
        is_fillna: bool,
        is_alpha: bool,
        num_groups: int,
        dtt_idx: pd.DatetimeIndex,
    ):
        super().__init__(
            array, "GroupReturn", sample_ts_length, sample_cs_length, sample_ts_step, is_fillna, num_groups, dtt_idx
        )
        self.is_alpha = is_alpha

    @property
    def stats(self):
        if self.num_groups == 1:
            gr_arr = self.array.flatten()
            gr_arr_ = gr_arr[np.isfinite(gr_arr)]
            return pd.DataFrame(
                {
                    0: {
                        "mean": np.mean(gr_arr_),
                        "std": np.std(gr_arr_),
                        "sharpe": np.mean(gr_arr_) / np.std(gr_arr_) if np.std(gr_arr_) > 0 else np.nan,
                        "count": gr_arr_.size,
                        "25%": np.percentile(gr_arr_, 25),
                        "50%": np.percentile(gr_arr_, 50),
                        "75%": np.percentile(gr_arr_, 75),
                    }
                }
            ).T
        return pd.concat([self.group([i]).stats.iloc[0].rename(i) for i in range(self.num_groups)], axis=1).T

    def group(self, idx: Sequence[int]):
        idx = [int(i) if i >= 0 else self.num_groups + i for i in idx]
        if max(idx) >= self.num_groups:
            raise ValueError(f"group index {idx} out of range, num_groups: {self.num_groups}")
        return self.__class__(
            array=self.array[:, :, idx],
            sample_ts_length=self.sample_ts_length,
            sample_cs_length=self.sample_cs_length,
            sample_ts_step=self.sample_ts_step,
            is_fillna=self.is_fillna,
            is_alpha=self.is_alpha,
            num_groups=len(idx),
            dtt_idx=self.dtt_idx,
        )


def binary_signal_transfer(signal: npt.NDArray[Union[np.float64, np.bool_]], full: int) -> npt.NDArray[np.float64]:
    """将二进制信号转换为仓位信号，并根据full参数决定是否把仓位调整到满仓

    Args:
        signal (npt.NDArray[Union[np.float64, np.bool_]]): 信号
        full (int): 如果full为0，则不调整仓位，否则信号数大于等于full时仓位调到满仓，小于full时每只股票的仓位为1/full

    Returns:
        npt.NDArray[np.float64]: 仓位信号
    """
    signal = signal.astype(np.float64)
    if full:
        sum_ = np.maximum(full, signal.sum(axis=1, keepdims=True))
    else:
        sum_ = signal.shape[1]
    ret = signal / sum_
    ret[~np.isfinite(ret)] = 0
    return ret


class FastBacktest:
    dtt_idx: pd.DatetimeIndex
    price_change: npt.NDArray[np.float64]
    signal: npt.NDArray[np.float64]
    cost: float
    cum_return_series: npt.NDArray[np.float64]
    position_ratio: npt.NDArray[np.float64]
    stats: Mapping

    def __init__(
        self,
        dtt_idx: pd.DatetimeIndex,
        price_change: npt.NDArray[np.float64],
        signal: npt.NDArray[np.float64],
        cost: float = 0.0,
    ):
        # TODO 现在只支持日频，分钟频需要再开发
        self.dtt_idx = dtt_idx
        self.price_change = price_change
        self.signal = signal
        self.cost = cost

        pc = self.price_change.copy().reshape((self.price_change.shape[0], -1))  # 2D
        valid_signal = self.signal.copy().reshape((self.signal.shape[0], -1))  # 2D
        valid_signal[~np.isfinite(pc)] = 0
        pc[~np.isfinite(pc)] = 0
        return_array = (pc - np.sign(valid_signal) * self.cost) * valid_signal  # 2D
        return_series = return_array.sum(axis=1)  # 1D

        self.cum_return_series = return_series.cumsum()  # 1D
        self.position_ratio = valid_signal.sum(axis=1)

        tmp1 = valid_signal != 0
        tmp2 = valid_signal.sum(axis=1) != 0
        self.stats = {
            "fr": self.cum_return_series[-1],
            "arr": self.cum_return_series[-1] / len(tmp2) * 252,
            "mdd": (np.maximum.accumulate(self.cum_return_series) - self.cum_return_series).max(),
            "count": (count := np.sum(tmp1)),
            "wr": np.sum(return_array > 0) / count if count > 0 else 0,
            "sharpe": np.mean(return_array[tmp1]) / np.std(return_array[tmp1])
            if np.std(return_array[tmp1]) > 0
            else np.nan,
            "dcount": (d_count := np.sum(tmp2)),
            "dwr": np.sum(return_series > 0) / d_count if d_count > 0 else 0,
            "dsharpe": np.mean(return_series[tmp2]) / np.std(return_series[tmp2])
            if np.std(return_series[tmp2]) > 0
            else np.nan,
        }

    def set(self, cost: Optional[float] = None, signal: Optional[npt.NDArray[np.float64]] = None):
        return FastBacktest(self.dtt_idx, self.price_change, signal or self.signal, cost or self.cost)

    def plot(self):
        _, ax1 = plt.subplots(figsize=(13, 4.5))
        sns.lineplot(self.cum_return_series, ax=ax1, label="Cumulative Return")
        pc = np.nancumsum(np.nanmean(self.price_change.reshape(self.price_change.shape[0], -1), axis=1))
        sns.lineplot(pc, ax=ax1, label="Price Change")
        sns.lineplot(self.cum_return_series - pc, ax=ax1, label="Alpha", linestyle="--")
        ax2 = ax1.twinx()
        sns.barplot(self.position_ratio, ax=ax2, alpha=0.3, color="lightgray")

        # 减少x轴刻度密度
        ax1.xaxis.set_major_locator(matplotlib.ticker.MaxNLocator("auto"))

        plt.show()


class BacktestResult(FactorAnalyseResult):
    hold_duration: int
    """持仓时长"""
    cost: float
    """手续费"""
    fb: Sequence[FastBacktest]

    def __init__(
        self,
        array: npt.NDArray[np.float64],
        sample_ts_length: int,
        sample_cs_length: int,
        is_fillna: bool,
        num_groups: int,
        hold_duration: int,
        cost: float,
        dtt_idx: pd.DatetimeIndex,
    ):
        super().__init__(array, "Backtest", sample_ts_length, sample_cs_length, 1, is_fillna, num_groups, dtt_idx)
        self.hold_duration = hold_duration
        self.cost = cost

        self.fb = []
        for i in range(self.num_groups):
            self.fb.append(
                FastBacktest(
                    self.dtt_idx,
                    price_change=self.array[:, :, i],
                    signal=np.full_like(self.array[:, :, i], 1 / self.hold_duration) / self.array.shape[1],
                    cost=self.cost,
                )
            )

    def group(self, idx: Sequence[int]):
        idx = [int(i) if i >= 0 else self.num_groups + i for i in idx]
        if max(idx) >= self.num_groups:
            raise ValueError(f"group index {idx} out of range, num_groups: {self.num_groups}")
        return self.__class__(
            array=self.array[:, :, idx],
            sample_ts_length=self.sample_ts_length,
            sample_cs_length=self.sample_cs_length,
            is_fillna=self.is_fillna,
            num_groups=len(idx),
            hold_duration=self.hold_duration,
            cost=self.cost,
            dtt_idx=self.dtt_idx,
        )

    @property
    def stats(self):
        if self.num_groups == 1:
            return pd.DataFrame({i: self.fb[i].stats for i in range(self.num_groups)}).T
        return pd.concat([self.group([i]).stats.iloc[0].rename(i) for i in range(self.num_groups)], axis=1).T

    def plot(self, **kwargs):
        name = "_".join(
            [
                self.method,
                "sample" + str((self.sample_ts_length, self.sample_cs_length, self.sample_ts_step)),
                str(self.hold_duration),
            ]
        ).replace(" ", "")
        if "title" in kwargs:
            name = kwargs["title"] + "_" + name

        figure, ax = plt.subplots(nrows=2, ncols=1, figsize=(13, 4.5 * 2))
        sns.lineplot(
            np.concatenate([self.fb[i].cum_return_series.reshape(-1, 1) for i in range(self.num_groups)], axis=1),
            ax=ax[0],
        )
        ax[0].legend(range(self.num_groups), ncol=np.ceil(self.num_groups / 5))

        df: pd.DataFrame = self.stats
        for col in df.columns:
            if col in ["count", "valid_count"]:
                df[col] = df[col].map(lambda x: f"{x:.0f}")
            else:
                df[col] = df[col].map(lambda x: f"{x:.4f}")
        table = ax[-1].table(
            cellText=df.values, rowLabels=df.index, colLabels=df.columns, cellLoc="center", loc="center"
        )
        table.auto_set_font_size(False)
        table.set_fontsize(14)
        table.scale(1, 1.5)  # 调整表格大小
        ax[-1].axis("off")
        plt.tight_layout()
        plt.show()

        if kwargs.get("savefig", False):
            pathlib.Path(kwargs["savefig"]).mkdir(parents=True, exist_ok=True)
            figure.savefig(pathlib.Path(kwargs["savefig"], name))


class FactorAnalyse:
    factor_arr: npt.NDArray[np.float64]
    """因子矩阵"""
    return_arr: npt.NDArray[np.float64]
    """收益率矩阵"""
    dtt_idx: pd.DatetimeIndex
    """时间索引"""

    def __init__(self, factor_df: pd.DataFrame, return_df: pd.DataFrame):
        self.factor_arr, self.return_arr, self.dtt_idx = align_df(factor_df, return_df)

    def ic(
        self, sample_ts_length: int, sample_cs_length: int, is_fillna: bool, num_groups: int, step: int = 0
    ) -> ICResult:
        """计算IC值

        Args:
            sample_ts_length (int): 时序上的采样长度, 传0时采样长度为时序的总长度
            sample_cs_length (int): 横截面的采样长度，传0时采样长度为横截面的总长度
            is_fillna (bool): 是否填充NaN
            num_groups (int): 分组数量，返回结果为每组内的IC
            step (int, optional): 采样步长. 传0时取sample_ts_length. Defaults to 0.

        Returns:
            ICResult: IC分析结果
        """

        res = iter_calc(
            method=0,
            factor_arr=self.factor_arr,
            return_arr=self.return_arr,
            sample_ts_length=sample_ts_length,
            sample_cs_length=sample_cs_length,
            sample_ts_step=step,
            is_fillna=is_fillna,
            is_centralize=False,
            num_groups=num_groups,
        )
        return ICResult(res, "IC", sample_ts_length, sample_cs_length, step, is_fillna, num_groups, self.dtt_idx)

    def rank_ic(
        self, sample_ts_length: int, sample_cs_length: int, is_fillna: bool, num_groups: int, step: int = 0
    ) -> ICResult:
        """计算RankIC值

        Args:
            sample_ts_length (int): 时序上的采样长度，传0时采样长度为时序的总长度
            sample_cs_length (int): 横截面的采样长度，传0时采样长度为横截面的总长度
            is_fillna (bool): 是否填充NaN
            num_groups (int): 分组数量，返回结果为每组内的RankIC
            step (int, optional): 采样步长. 传0时取sample_ts_length. Defaults to 0.

        Returns:
            ICResult: IC分析结果
        """
        res = iter_calc(
            method=1,
            factor_arr=self.factor_arr,
            return_arr=self.return_arr,
            sample_ts_length=sample_ts_length,
            sample_cs_length=sample_cs_length,
            sample_ts_step=step,
            is_fillna=is_fillna,
            is_centralize=False,
            num_groups=num_groups,
        )
        return ICResult(res, "RankIC", sample_ts_length, sample_cs_length, step, is_fillna, num_groups, self.dtt_idx)

    def norm_ic(
        self, sample_ts_length: int, sample_cs_length: int, is_fillna: bool, num_groups: int, step: int = 0
    ) -> ICResult:
        """计算NormIC值

        Args:
            sample_ts_length (int): 时序上的采样长度，传0时采样长度为时序的总长度
            sample_cs_length (int): 横截面的采样长度，传0时采样长度为横截面的总长度
            is_fillna (bool): 是否填充NaN
            num_groups (int): 分组数量，返回结果为每组内的NormIC
            step (int, optional): 采样步长. 传0时取sample_ts_length. Defaults to 0.

        Returns:
            ICResult: IC分析结果
        """
        res = iter_calc(
            method=5,
            factor_arr=self.factor_arr,
            return_arr=self.return_arr,
            sample_ts_length=sample_ts_length,
            sample_cs_length=sample_cs_length,
            sample_ts_step=step,
            is_fillna=is_fillna,
            is_centralize=False,
            num_groups=num_groups,
        )
        return ICResult(res, "NormIC", sample_ts_length, sample_cs_length, step, is_fillna, num_groups, self.dtt_idx)

    def group_return(
        self,
        sample_ts_length: int,
        sample_cs_length: int,
        is_fillna: bool,
        is_alpha: bool,
        num_groups: int,
        step: int = 0,
    ) -> GroupReturnResult:
        """计算分组收益

        Args:
            sample_ts_length (int): 时序上的采样长度，传0时采样长度为时序的总长度
            sample_cs_length (int): 横截面的采样长度，传0时采样长度为横截面的总长度
            is_fillna (bool): 是否填充NaN
            is_alpha (bool): 是否中心化，求的是超额收益
            num_groups (int): 分组数量
            step (int, optional): 采样步长. 传0时取sample_ts_length. Defaults to 0.

        Returns:
            GroupReturnResult: 分组收益结果
        """
        res = iter_calc(
            method=3,
            factor_arr=self.factor_arr,
            return_arr=self.return_arr,
            sample_ts_length=sample_ts_length,
            sample_cs_length=sample_cs_length,
            sample_ts_step=step,
            is_fillna=is_fillna,
            is_centralize=is_alpha,
            num_groups=num_groups,
        )
        return GroupReturnResult(
            res, sample_ts_length, sample_cs_length, step, is_fillna, is_alpha, num_groups, self.dtt_idx
        )

    def backtest(
        self,
        sample_ts_length: int,
        sample_cs_length: int,
        is_fillna: bool,
        is_alpha: bool,
        num_groups: int,
        hold_duration: int = 0,
        cost: float = 0.0,
    ) -> BacktestResult:
        """计算回测结果

        Args:
            sample_ts_length (int): 时序上的采样长度，传0时采样长度为时序的总长度
            sample_cs_length (int): 横截面的采样长度，传0时采样长度为横截面的总长度
            is_fillna (bool): 是否填充NaN
            is_alpha (bool): 是否中心化，求的是超额收益
            num_groups (int): 分组数量
            hold_duration (int, optional): 持仓时长. Defaults to 0.
            cost (float, optional): 交易成本. Defaults to 0.0.

        Returns:
            BacktestResult: 回测结果
        """
        res = iter_calc(
            method=4,
            factor_arr=self.factor_arr,
            return_arr=self.return_arr,
            sample_ts_length=sample_ts_length,
            sample_cs_length=sample_cs_length,
            sample_ts_step=1,
            is_fillna=is_fillna,
            is_centralize=is_alpha,
            num_groups=num_groups,
        )
        return BacktestResult(
            res, sample_ts_length, sample_cs_length, is_fillna, num_groups, hold_duration, cost, self.dtt_idx
        )
