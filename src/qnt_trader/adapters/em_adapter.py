from typing import Dict, List, Optional

import pandas as pd

from em_trade_api import EMCreditTradeAPI, EMTradeAPI
from qnt_utils.enums import Direction, OffSet, OrderStatus, PositionType, PriceType
from qnt_utils.label import QSymbol, Symbol

from ..exceptions import trade_api_exc
from .base import TradeAPIAdapter


def _fnc1(status):
    # TODO:
    if "撤" in status:
        return OrderStatus.CANCELLED
    elif status == "已成":
        return OrderStatus.FINISHED
    elif status == "未报":
        return OrderStatus.ORDER_TBC
    else:
        return OrderStatus.ALIVE


def _fnc2(wtfx, jylb):
    if wtfx == "证券买入":
        if jylb == "买入担保品":
            return Direction.BUY
        elif jylb == "融资开仓":
            return Direction.CREDIT_BUY
    elif wtfx == "证券卖出":
        # TODO:融券
        return Direction.SELL


def _fnc3(wtfx, jylb):
    # TODO:
    if wtfx == "证券买入":
        return OffSet.OPEN
    elif wtfx == "证券卖出":
        # TODO:融券
        return OffSet.CLOSE


class EMTradeAPIAdapter(TradeAPIAdapter):
    MARKET = ["SSE", "SZSE"]

    def __init__(self, account, password):
        self._trade_api = EMTradeAPI(account=account, password=password)
        # self._trade_api =None
        super().__init__(account, password, False)

    @property
    @trade_api_exc
    def portfolio(self) -> Dict:
        res = self._trade_api.portfolio
        return {
            "asset": res["总资产"],
            "debt": 0,
            "equity": res["总资产"],
            "available_cash": res["可用资金"],
            "frozen_cash": res["冻结资金"],
            "market_value": res["总市值"],
            "margin": 0,
            "available_credit": 0,
            "interest_of_financed_funds": 0,
            "interest_of_financed_bonds": 0,
        }

    @property
    @trade_api_exc
    def positions(self) -> Dict:
        res = self._trade_api.positions
        tmp = [
            {
                "symbol": Symbol.qs_from_em("{}.{}".format(i["证券代码"], i["交易市场"])),
                "amount": i["证券数量"],
                "available": i["可用数量"],
                "frozen": i["证券数量"] - i["可用数量"],
                "amount_today": 0,
                "amount_his": 0,
                "amount_today_available": 0,
                "amount_his_available": 0,
                "cost_basis": i["成本价格"],
                "last_price": i["最新价格"],
                "margin": 0,
                "market_value": i["最新市值"],
                "profit": i["累计盈亏"],
                "profit_rate": i["盈亏比例"],
                "name": i["证券名称"],
                "position_type": PositionType.LONG,
            }
            for i in res
        ]
        return self._positions_to_dict(tmp)

    @trade_api_exc
    def order(
        self,
        symbol: QSymbol | str,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: PriceType = PriceType.LIMIT,
    ) -> str:
        direction_ = direction if isinstance(direction, Direction) else Direction[direction]
        if direction_ not in [Direction.BUY, Direction.SELL]:
            raise
        res = self._trade_api.order(
            stock_code=Symbol.get_pure_symbol(symbol),
            price=price,
            amount=int(amount),
            money=None,
            trade_type={Direction.BUY: 1, Direction.SELL: 2}[direction_],
        )
        return "EM_{}".format(res["委托编号"])

    @trade_api_exc
    def cancel_order(self, api_order_id: str) -> None:
        """撤单"""
        self._trade_api.cancel_order(api_order_id.replace("EM_", ""))
        return

    @trade_api_exc
    def get_orders(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List:
        """获取普通账号委托记录

        Args:
            start_date (Optional[str], optional): 开始日期. Defaults to None.
            end_date (Optional[str], optional): 结束日期. Defaults to None.

        Returns:
            List: 委托记录
        """
        start_date = pd.Timestamp.now().strftime("%Y-%m-%d") if start_date is None else start_date
        end_date = pd.Timestamp.now().strftime("%Y-%m-%d") if end_date is None else end_date
        res = self._trade_api.get_orders(start_date, end_date)
        return [
            {
                "c_dt": pd.Timestamp("{} {:0>10.3f}".format(i["委托日期"], i["委托时间"])).strftime(
                    "%Y-%m-%d %H:%M:%S.%f"
                ),
                "m_dt": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "symbol": Symbol.qs_from_em("{}.{}".format(i["证券代码"], i["交易市场"])),
                "name": i["证券名称"],
                "direction": {"证券买入": Direction.BUY, "证券卖出": Direction.SELL}.get(i["委托方向"]),
                "offset": {"证券买入": OffSet.OPEN, "证券卖出": OffSet.CLOSE}.get(i["委托方向"]),
                "amount": float(i["委托数量"]),
                "price": i["委托价格"],
                "status": _fnc1(i["委托状态"]),
                "price_type": PriceType.LIMIT,
                "trade_price": i["成交价格"],
                "trade_amount": float(i["成交数量"]),
                "api_order_id": "EM_{}".format(i["委托编号"]),
            }
            for i in res
            if i["委托方向"] in ["证券买入", "证券卖出"]
        ]


class EMCreditTradeAPIAdapter(TradeAPIAdapter):
    MARKET = ["SSE", "SZSE"]

    # TODO 接口自定义异常

    def __init__(self, account, password):
        self._trade_api = EMCreditTradeAPI(account=account, password=password)
        # self._trade_api=None
        super().__init__(account, password, True)

    @property
    @trade_api_exc
    def portfolio(self) -> Dict:
        res = self._trade_api.portfolio
        return {
            "asset": res["总资产"],
            "debt": res["总负债"],
            "equity": res["总资产"] - res["总负债"],
            "available_cash": res["可用资金"],
            "frozen_cash": res["资金余额"] - res["可用资金"],
            "market_value": res["总市值"],
            "margin": 0,
            "available_credit": res["可用保证金"],
            "interest_of_financed_funds": res["融资利率"],
            "interest_of_financed_bonds": res["融券利率"],
        }

    @property
    @trade_api_exc
    def positions(self) -> Dict:
        res = self._trade_api.positions
        tmp = [
            {
                "symbol": Symbol.qs_from_em("{}.{}".format(i["证券代码"], i["交易市场"])),
                "amount": i["证券数量"],
                "available": i["可用数量"],
                "frozen": i["证券数量"] - i["可用数量"],
                "amount_today": 0,
                "amount_his": 0,
                "amount_today_available": 0,
                "amount_his_available": 0,
                "cost_basis": i["成本价格"],
                "last_price": i["最新价格"],
                "margin": 0,
                "market_value": i["最新市值"],
                "profit": i["累计盈亏"],
                "profit_rate": i["盈亏比例"],
                "name": i["证券名称"],
                "position_type": PositionType.LONG,
            }
            for i in res
        ]
        return self._positions_to_dict(tmp)

    @trade_api_exc
    def order(
        self,
        symbol: QSymbol | str,
        direction: str | Direction,
        offset: str | OffSet,
        amount: float,
        price: float,
        price_type: PriceType = PriceType.LIMIT,
    ) -> str:
        direction_ = direction if isinstance(direction, Direction) else Direction[direction]
        res = self._trade_api.order(
            stock_code=Symbol.get_pure_symbol(symbol),
            price=price,
            amount=int(amount),
            money=None,
            trade_type={Direction.BUY: 1, Direction.SELL: 2, Direction.CREDIT_BUY: 3, Direction.CREDIT_SELL: 4}[
                direction_
            ],
        )
        return "EMC_{}".format(res["委托编号"])

    @trade_api_exc
    def cancel_order(self, api_order_id) -> None:
        """撤单"""
        self._trade_api.cancel_order(api_order_id.replace("EMC_", ""))
        return

    @trade_api_exc
    def get_orders(self, start_date: Optional[str] = None, ed: Optional[str] = None) -> List:
        """获取信用账户委托记录

        Args:
            start_date (Optional[str], optional): 开始日期. Defaults to None.
            ed (Optional[str], optional): 结束日期. Defaults to None.

        Returns:
            List: 委托记录
        """
        start_date = pd.Timestamp.now().strftime("%Y-%m-%d") if start_date is None else start_date
        ed = pd.Timestamp.now().strftime("%Y-%m-%d") if ed is None else ed
        res = self._trade_api.get_orders(start_date, ed)
        return [
            {
                "c_dt": pd.Timestamp("{} {:0>10.3f}".format(i["委托日期"], i["委托时间"])).strftime(
                    "%Y-%m-%d %H:%M:%S.%f"
                ),
                "m_dt": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "symbol": Symbol.qs_from_em("{}.{}".format(i["证券代码"], i["交易市场"])),
                "name": i["证券名称"],
                "direction": _fnc2(i["委托方向"], i["交易类别"]),
                "offset": _fnc3(i["委托方向"], i["交易类别"]),
                "amount": float(i["委托数量"]),
                "price": i["委托价格"],
                "status": _fnc1(i["委托状态"]),
                "price_type": PriceType.LIMIT,
                "trade_price": i["成交价格"],
                "trade_amount": float(i["成交数量"]),
                "api_order_id": "EMC_{}".format(i["委托编号"]),
            }
            for i in res
            if i["委托方向"] in ["证券买入", "证券卖出"]
        ]
