import datetime
import fcntl
import pathlib
import pickle
import re
from functools import lru_cache
from itertools import product
from typing import Dict, List, Literal, Optional, Tuple, Union

import mplfinance as mpf
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt

from aichemy.utils import from_nstimestamp, to_nstimestamp

try:
    from DataAPI.openapi.open_api import OpenAPI
    from mindgo_api import *

    gl_all_trade_days: pd.DatetimeIndex = get_all_trade_days()

    sql_api = OpenAPI()
    # "股票剔除日期"都是空的，先不处理了
    gl_concept_data = sql_api.get_all_concepts()
    gl_concept_data = gl_concept_data.loc[
        gl_concept_data["板块名称"].apply(lambda x: "同花顺" not in x)
    ]  # 剔除'同花顺漂亮100', '同花顺中特估100', '同花顺出海50', '同花顺果指数', '同花顺新质50'这些
    gl_concept_data["行情代码"] = gl_concept_data["行情代码"].apply(lambda x: x + ".TI")
    gl_concept_data = gl_concept_data.query("行情代码 != '885896.TI'")
    gl_concept_data["股票纳入日期"] = pd.to_datetime(gl_concept_data["股票纳入日期"])
    gl_concept_data["插入时间"] = pd.to_datetime(gl_concept_data["插入时间"])
    gl_concept_ctime = gl_concept_data.groupby("行情代码")[
        "插入时间"
    ].min()  # 概念板块的成立时间, 这里的时间的次日，概念才生效

except ImportError:
    pass


def get_stock_name(symbol):
    return get_security_info(symbol).display_name


@lru_cache(maxsize=1024)
def get_next_trade_day(d):
    """获取下一个交易日"""
    global gl_all_trade_days
    return gl_all_trade_days[np.searchsorted(gl_all_trade_days, pd.Timestamp(d).date(), "right")]


def get_trade_day_pair():
    """获取  向前推300个交易日  到  下一交易日"""
    today = pd.Timestamp.now()
    global gl_all_trade_days
    trade_days = list(map(lambda x: x.replace(hour=15), gl_all_trade_days))
    index = np.searchsorted(trade_days, today, "right")
    return gl_all_trade_days[index - 300].strftime("%Y-%m-%d"), gl_all_trade_days[index].strftime("%Y-%m-%d")


def plot_klines(symbol, start_dt, end_dt, fre, bar_count):
    df = get_price(symbol, start_dt, end_dt, fre, ["open", "high", "low", "close", "volume"], bar_count=bar_count)
    mpf.plot(df, type="candle", mav=(5, 10), volume=True)


def plot_intraday_chart(symbol, end_dt):
    df = get_price(
        symbol,
        pd.Timestamp(f"{end_dt} 0000"),
        pd.Timestamp(f"{end_dt} 1500"),
        "1m",
        ["open", "high", "low", "close", "volume"],
    )
    mpf.plot(df, type="candle", mav=(5, 10), volume=True)


def get_reach_high_limit_stocks(symbols: List[str], end_dt: str, bar_count=1) -> Optional[pd.DataFrame]:
    """获取指定日期涨停股票的涨幅、涨停时间、封板时间、连续涨停天数等

    Args:
        symbols (List[str]): 股票代码列表
        end_dt (str): 指定日期
        bar_count (int, optional): 回溯多少天. Defaults to 1.

    Returns:
        Optional[pd.DataFrame]: None - 股票列表中在指定日期没有涨停的股票; pd.DataFrame - index为股票代码，columns为["名称", "涨幅(%)", "涨停时间", "封板时间", "涨停天数", "连续涨停天数"]
    """
    end_dt = pd.Timestamp(end_dt).strftime("%Y-%m-%d")

    # 1. 获取end_dt当日涨停股票和涨幅
    df = get_price(
        symbols,
        end_dt,
        end_dt,
        "1d",
        ["close", "high_limit", "quote_rate", "turnover_rate", "turnover", "open", "prev_close"],
        skip_paused=True,
        fq=None,
        is_panel=True,
    )
    df["turnover"] /= 1e8
    df["open"] = df["open"] / df["prev_close"] * 100 - 100
    mask = df["close"] >= df["high_limit"]
    tmp = []
    for i, j in {
        "open": f"{end_dt}开盘涨幅(%)",
        "quote_rate": f"{end_dt}涨幅(%)",
        "turnover_rate": f"{end_dt}换手率(%)",
        "turnover": f"{end_dt}成交额(亿)",
    }.items():
        tmp.append(df[i][mask].dropna(axis=1).iloc[0].rename(j))
    df = pd.concat(tmp, axis=1)
    df["名称"] = df.index.map(get_stock_name)
    df = df.reindex(columns=["名称"] + df.columns.difference(["名称"]).tolist())
    if df.empty:
        return None

    # 2. 获取end_dt当日涨停股票的涨停时间和封板时间
    b = get_tick(df.index.tolist(), f"{end_dt} 09:15", f"{end_dt} 15:30", ["current", "high"])
    b = b.dropna(subset=["high", "current"], how="any", axis=0)
    c1 = get_price_df(df.index.tolist(), end_dt, end_dt, "1d", ["high_limit"], fq=None)["high_limit"].iloc[0]

    c = b.set_index(["trade_date", "id_stock"])["high"].unstack().ffill()
    c = c[c.eq(c1, axis=1)].apply(lambda col: col.first_valid_index(), axis=0)
    c.index.rename(None, inplace=1)
    df = pd.concat([df, c.rename(f"{end_dt}涨停时间")], axis=1).sort_values(f"{end_dt}涨停时间")

    c = b.set_index(["trade_date", "id_stock"])["current"].unstack().ffill()
    c = c[~c.eq(c1, axis=1)].apply(lambda col: col.shift(1).last_valid_index(), axis=0)
    c.index.rename(None, inplace=1)
    df = pd.concat([df, c.rename(f"{end_dt}封板时间")], axis=1)

    # 3. 获取end_dt当日涨停股票的连续涨停天数
    tmp = get_price(
        df.index.tolist(),
        None,
        end_dt,
        "1d",
        ["close", "high_limit"],
        fq="pre",
        skip_paused=True,
        bar_count=bar_count,
        is_panel=True,
    )
    t1: pd.DataFrame = tmp["close"] == tmp["high_limit"]
    df[f"{bar_count}日内涨停天数"] = t1.sum(axis=0, skipna=True)
    for i in t1.columns.tolist():
        count = 0
        for j in t1.index.tolist()[::-1]:
            if t1.loc[j, i]:
                count += 1
            else:
                break
        df.loc[i, "连续涨停天数"] = count

    return df


def plot_multi_bar(df_dict: Dict[str, pd.DataFrame], figsize=(25, 9)):
    fig, ax = plt.subplots(len(df_dict), 1, sharex=True, figsize=figsize)

    for n_df, (k, df) in enumerate(df_dict.items()):
        l1, l2 = df.shape
        width = 0.8 / l1
        for e, (i, j) in enumerate(df.iterrows()):
            height = j.values
            positions = np.arange(l2) + e * width
            ax[n_df].bar(positions, height, width=width, label=from_nstimestamp(i).strftime("%m%d"))
        ax[n_df].set_xticks(list(np.arange(l2) + (l1 - 1) * width / 2))
        ax[n_df].set_xticklabels(df.columns.tolist())
        ax[n_df].set_title(k)

    plt.legend(fontsize=8)
    plt.show()


def calc_trade_day(
    td: Union[str, pd.Timestamp], delta_days: int, all_trade_days: List[pd.Timestamp] = None
) -> pd.Timestamp:
    """计算指定日期加上或减去指定天数后的交易日

    Args:
        td (Union[str, pd.Timestamp]): 指定日期
        delta_days (int): 偏移天数. 0表示最近一个交易日，在最近一个交易日上进行偏移.
        all_trade_days (List[pd.Timestamp], optional): 交易日列表. Defaults to None.

    Returns:
        pd.Timestamp: _description_
    """
    all_trade_days = all_trade_days or gl_all_trade_days
    td = pd.Timestamp(pd.Timestamp(td).date())
    td = all_trade_days[np.searchsorted(all_trade_days, td, "right") - 1]
    if delta_days > 0:
        return all_trade_days[np.searchsorted(all_trade_days, td, "right") + delta_days - 1]
    elif delta_days < 0:
        return all_trade_days[np.searchsorted(all_trade_days, td, "left") + delta_days]
    else:
        return td


def get_latest_trade_day() -> pd.Timestamp:
    """获取最近一个交易日，无时区"""
    return calc_trade_day("now", 0)


def transform_time_index_to_nstimestamp(index, ty="DAILY"):
    """supermind上是无时间的时间，转换为ns时间戳"""
    ret = pd.to_datetime(index).astype(int) - int(8 * 3600 * 1e9)
    if ty == "DAILY":
        ret += int(15 * 3600 * 1e9)
    return ret


def get_market_info(start_dt, end_dt, freq, fields):
    """获取全市场的信息"""
    data = get_price(["000001.SH", "399106.SZ"], start_dt, end_dt, freq, fields, is_panel=True)
    ret = pd.concat([data[i].sum(axis=1).rename(i) for i in fields], axis=1)
    ret.index = transform_time_index_to_nstimestamp(ret.index, "DAILY")
    return ret


@lru_cache
def query_market_cap_table() -> pd.DataFrame:
    """获取每只股票的总市值

    Returns:
        pd.DataFrame: index - 日期，columns - 股票代码
    """
    tmp = run_query(query(factor.symbol, factor.date, factor.market_cap))
    tmp_ = tmp.set_index(["factor_date", "factor_symbol"])
    tmp_ = tmp_["factor_market_cap"].unstack()
    tmp_.index.rename(None, inplace=True)
    tmp_.columns.rename(None, inplace=True)
    return tmp_


@lru_cache
def query_liquid_market_cap_table() -> pd.DataFrame:
    """获取每只股票的流通市值

    Returns:
        pd.DataFrame: index - 日期，columns - 股票代码
    """
    tmp = run_query(query(factor.symbol, factor.date, factor.current_market_cap))
    tmp_ = tmp.set_index(["factor_date", "factor_symbol"])
    tmp_ = tmp_["factor_current_market_cap"].unstack()
    tmp_.index.rename(None, inplace=True)
    tmp_.columns.rename(None, inplace=True)
    return tmp_


@lru_cache(20000)
def get_market_cap(symbol: str, dt: str, type_: str = "total") -> float:
    """获取指定股票指定日期的市值

    Args:
        symbol (str): 股票代码
        dt (str): 日期

    Returns:
        float: 指定股票指定日期的市值
    """
    if type_ == "total":
        return query_market_cap_table().get(symbol, {}).get(pd.Timestamp(dt).strftime("%Y-%m-%d"), 0)
    elif type_ == "liquid":
        return query_liquid_market_cap_table().get(symbol, {}).get(pd.Timestamp(dt).strftime("%Y-%m-%d"), 0)
    else:
        raise ValueError(f"type_ must be 'total' or 'liquid', but got {type_}")


# @lru_cache(2000)
# def get_concept_stocks_(concept_symbol: str, date: str = "now") -> List[str]:
#     """获取概念板块成分股

#     Args:
#         concept_symbol (str): 概念板块代码
#         date (str, optional): 日期，默认为"now"

#     Returns:
#         List[str]: 概念板块的成分股
#     """
#     tmp = get_all_securities("stock", date=date).index.tolist()
#     return [i for i in get_concept_stocks(concept_symbol, date=date) if (i != "" and i in tmp)]


@lru_cache(50)
def _get_concept_stocks_dict_by_date(date: str):
    global gl_concept_data

    tmp = get_all_securities_(date, date)
    date = pd.to_datetime(date).strftime("%Y-%m-%d 15:00")
    return (
        gl_concept_data.loc[(gl_concept_data["插入时间"] < pd.Timestamp(date))]
        .groupby("行情代码")["股票代码"]
        .apply(lambda x: [i for i in x if i in tmp])
        .to_dict()
    )


@lru_cache(2000)
def get_concept_stocks_(concept_symbol: str, date: str = "now") -> List[str]:
    """获取概念板块成分股"""
    return _get_concept_stocks_dict_by_date(date).get(concept_symbol, [])


# @lru_cache(2000)
# def get_A_concept(date: str = "now") -> pd.DataFrame:
#     """获取A股概念

#     Args:
#         date (str, optional): 日期，默认为"now"


#     Returns:
#         pd.DataFrame: index - 概念板块的中文名称，columns - 'concept_symbol', 'concept_code', 'concept_level', 'concept_levelname', 'concept_rank', 'concept_thscode', 'available_date', 'disabled_date'
#     """
#     a = get_concept_relate(date=date, levels=None, fields=None)
#     a = a.loc[a["concept_levelname"].apply(lambda x: not x is None and "A股" in x)]
#     return a.loc[~a["concept_thscode"].isna()]


@lru_cache(2000)
def get_A_concept(date: str = "now") -> pd.DataFrame:
    """获取A股概念， 指定日期盘中新增的也可以查到"""
    global gl_concept_data, gl_concept_ctime

    date = pd.to_datetime(date).strftime("%Y-%m-%d 15:00")
    concept_list = gl_concept_ctime[gl_concept_ctime < pd.Timestamp(date)].index
    res = (
        gl_concept_data.loc[gl_concept_data["行情代码"].isin(concept_list)][["板块名称", "行情代码"]]
        .drop_duplicates()
        .set_index("板块名称")
    )
    res.index.rename("concept_name", inplace=True)
    res.rename(columns={"行情代码": "concept_thscode"}, inplace=True)
    res["concept_levelname"] = "A股_常规概念"
    return res


@lru_cache(2000)
def get_concept_name(concept_symbol: str, date: str = "now") -> Tuple[str, str, str]:
    """获取概念板块名称等相关信息

    Args:
        concept_symbol (str): 概念板块代码
        date (str, optional): 日期，默认为"now"

    Returns:
        Tuple[str,str,str]: 概念板块代码，概念板块等级，概念板块中文名称
    """
    tmp: pd.DataFrame = get_A_concept(date=date)
    tmp1 = dict(zip(tmp["concept_thscode"], tmp.index))
    tmp2 = dict(zip(tmp["concept_thscode"], tmp["concept_levelname"]))
    return concept_symbol, tmp2.get(concept_symbol, None), tmp1.get(concept_symbol, None)


def get_stock_related_concepts(stock_symbol: str, date: str = "now") -> List[Tuple[str, str, str]]:
    """获取股票相关概念

    Args:
        stock_symbol (str): 股票代码
        date (str, optional): 日期，默认为"now"

    Returns:
        List[Tuple[str, str, str]]: 概念板块代码，概念板块等级，概念板块中文名称
    """
    ret = []
    for i in get_A_concept(date=date)["concept_thscode"]:
        if stock_symbol in get_concept_stocks_(concept_symbol=i, date=date):
            ret.append(get_concept_name(concept_symbol=i, date=date))
    return ret


@lru_cache(2000)
def get_industry_stocks_(industry_symbol: str, date: str = "now") -> List[str]:
    """获取行业板块成分股

    Args:
        industry_symbol (str): 行业板块代码
        date (str, optional): 日期，默认为"now"

    Returns:
        List[str]: 行业板块的成分股
    """
    return get_industry_stocks(industry_symbol, date=date)


def get_price_(symbols, *args, **kwargs):
    while len(symbols) > 0:
        try:
            t = get_price(symbols, *args, **kwargs)
            break
        except Exception as e:
            remove_symbol = re.findall(r"[0-9]{6}\.[A-Z]{2}", str(e))
            if len(remove_symbol) == 0:
                raise e
            print(f"移除股票: {remove_symbol}")
            symbols = [i for i in symbols if i not in remove_symbol]
            if len(symbols) == 0:
                raise ValueError("没有可用的股票")
    return t


@lru_cache(2000)
def get_A_industries(date: str = "now") -> pd.DataFrame:
    """获取A股行业, 主要是同花顺二级行业，申万二级行业，中信二级行业

    Args:
        date (str, optional): 日期，默认为"now"

    Returns:
        pd.DataFrame: index - 序号，columns - 'name', 'industry_symbol', 'industry_thscode', 'industry_type'
    """

    old_industry_code_dict = {
        "industryid2": {
            "T0203": "881107.TI",
            "T0602": "881116.TI",
            "T0704": "881120.TI",
            "T1001": "881129.TI",
            "T1902": "881156.TI",
            "T1903": "881157.TI",
            "T2201": "881162.TI",
            "T2202": "881163.TI",
        }
    }

    ret = []
    for i in ["industryid2", "s_industryid2", "ci_industryid2"]:
        tmp = get_industry_relate(date=date, types=i)
        tmp["industry_thscode"] = tmp.apply(
            lambda x: x["industry_thscode"]
            if not pd.isna(x["industry_thscode"])
            else old_industry_code_dict.get(i, {}).get(x["industry_symbol"], np.nan),
            axis=1,
        )
        tmp["name"] = tmp.index
        tmp["industry_type"] = {
            "industryid2": "同花顺二级行业",
            "s_industryid2": "申万二级行业",
            "ci_industryid2": "中信二级行业",
        }.get(i)
        ret.append(tmp)
    ret = pd.concat(ret, axis=0).reset_index()[["name", "industry_symbol", "industry_thscode", "industry_type"]]
    if len(ret["industry_symbol"].unique()) < len(ret):
        raise RuntimeError("重复行业代码")
    return ret


@lru_cache(2000)
def get_industry_name(industry_symbol: str, date: str = "now") -> Tuple[str, str, str, str]:
    """获取行业板块名称等相关信息，主要是同花顺二级行业，申万二级行业，中信二级行业

    Args:
        industry_symbol (str): 行业代码，如T201，不是行业指数代码
        date (str, optional): 日期，默认为"now"

    Returns:
        Tuple[str, str, str, str]: 行业板块指数代码, 行业类型，行业中文名称，行业代码
    """
    tmp = get_A_industries(date=date).copy()
    tmp.set_index("industry_symbol", inplace=True)
    return (
        tmp["industry_thscode"].loc[industry_symbol],
        tmp["industry_type"].loc[industry_symbol],
        tmp["name"].loc[industry_symbol],
        industry_symbol,
    )


def get_stock_related_industries(stock_symbol: str, date: str = "now") -> List[Tuple[str, str, str, str]]:
    """获取股票相关行业

    Args:
        stock_symbol (str): 股票代码

    Returns:
        List[Tuple[str, str, str]]: 行业板块指数代码, 行业类型，行业中文名称，行业代码。如果股票没有相关行业，则返回空列表
        例如[(None, '同花顺二级行业', '计算机应用', 'T2202'), ('801151.SL', '申万二级行业', '化学制药', 'S3701'), ('CI005152.CI', '中信二级行业', '化学制药', 'CI351000')]
    """
    ret = []
    tmp = get_symbol_industry(stock_symbol, date)
    if tmp is None:
        return ret
    for i in [tmp.industryid2, tmp.s_industryid2, tmp.ci_industryid2]:
        if i != "":
            ret.append(get_industry_name(i, date=date))
    return ret


def get_pct_rank(
    symbols: List[str], bar_count: int = 5, date: str = "now", base: Literal["low", "open", "prev_close"] = "low"
) -> pd.DataFrame:
    """获取股票列表的涨跌幅和排名

    Args:
        symbols (List[str]): 股票代码
        bar_count (int, optional): 数据长度. Defaults to 5.
        date (str, optional): 日期，默认为"now"
        base (str, optional): 类型，默认为"low"，low - 从近几日最低点计算至今的涨幅，open - 表示从第1日的开盘价开始，prev_close - 表示从第1日的昨收价开始

    Returns:
        pd.DataFrame: index - 股票代码，columns - 'pct', 'rank', 'count'
    """
    if len(symbols) == 0:
        return pd.DataFrame()
    t = get_price_(
        symbols,
        None,
        date,
        "1d",
        ["low", "close", "turnover", "prev_close"],
        fq="pre",
        skip_paused=False,
        bar_count=bar_count,
        is_panel=True,
    )
    trn = t["turnover"].iloc[-1] / 1e8
    if base == "low":
        t = (t["close"].iloc[-1] / t["low"].min(axis=0, skipna=True)) - 1
    elif base == "open":
        first_non_nan_values = t["open"].apply(lambda col: col[col.first_valid_index()], axis=0)
        t = t["close"].iloc[-1] / first_non_nan_values - 1
    elif base == "prev_close":
        first_non_nan_values = t["prev_close"].apply(lambda col: col[col.first_valid_index()], axis=0)
        t = t["close"].iloc[-1] / first_non_nan_values - 1
    else:
        raise ValueError(f"type_ must be 'low' or 'open', but got {base}")
    t = pd.concat([t.rename("pct"), t.rank(ascending=False).rename("rank"), trn.rename("turnover")], axis=1)
    t["count"] = len(t)
    return t


@lru_cache(2000)
def get_all_concept_pct_rank(bar_count: int = 5, date: str = "now", type_: str = "low"):
    """获取所有概念板块涨跌幅和排名，返回字典，key为概念等级，value为index为concept_symbol，columns为'concept_pct', 'concept_rank', 'concept_count', 'concept_turnover'"""
    ret = {}
    for i, j in get_A_concept(date=date).groupby("concept_levelname"):
        ret[i] = get_pct_rank(j["concept_thscode"].tolist(), bar_count, date, type_)
        ret[i].rename(
            columns={
                "rank": "concept_rank",
                "pct": "concept_pct",
                "count": "concept_count",
                "turnover": "concept_turnover",
            },
            inplace=True,
        )
    return ret


@lru_cache(2000)
def get_concept_stocks_pct_rank(concept_symbol, bar_count, date, base: Literal["low", "open", "prev_close"] = "low"):
    return get_pct_rank(get_concept_stocks_(concept_symbol=concept_symbol, date=date), bar_count, date, base)


def get_concept_leader_stocks_pct_rank(concept_symbol: str, bar_count: int, date: str):
    df = get_reach_high_limit_stocks(get_concept_stocks_(concept_symbol=concept_symbol, date=date), date, bar_count)
    if df is None:
        return None
    df1 = get_concept_stocks_pct_rank(concept_symbol=concept_symbol, bar_count=bar_count, date=date, base="prev_close")
    return pd.concat(
        [
            df,
            (df1["pct"] * 100).rename(f"{bar_count}日累计涨幅(%)").loc[df.index],
            df1.apply(lambda x: f"{x['rank']:.0f}/{x['count']:.0f}", axis=1)
            .rename(f"{bar_count}日累计涨幅排名")
            .loc[df.index],
        ],
        axis=1,
    )


def get_concept_new_member(concept_symbol, date):
    a = get_concept_stocks_(concept_symbol=concept_symbol, date=date)
    b = get_concept_stocks_(concept_symbol=concept_symbol, date=calc_trade_day(date, -1))
    c = list(set(a) - set(b))
    return pd.Series(map(lambda x: get_stock_name(x), c), index=c)


@lru_cache(2000)
def get_all_industries_pct_rank(bar_count: int = 5, date: str = "now", type_: str = "low") -> Dict[str, pd.DataFrame]:
    """获取所有行业板块涨跌幅和排名

    Args:
        bar_count (int, optional): 统计的历史bar数量. Defaults to 5.
        date (str, optional): 截止日期. Defaults to "now".

    Returns:
        Dict[str, pd.DataFrame]: key: 行业分类标准，value: index为行业名称，columns为'industry_pct', 'industry_rank', 'industry_count'
    """
    ret = {}
    for i, j in get_A_industries(date=date).groupby("industry_type"):
        ret[i] = get_pct_rank(j["industry_thscode"].dropna().tolist(), bar_count, date, type_)
        ret[i].rename(
            columns={
                "rank": "industry_rank",
                "pct": "industry_pct",
                "count": "industry_count",
                "turnover": "industry_turnover",
            },
            inplace=True,
        )
    return ret


@lru_cache(2000)
def get_industry_stocks_pct_rank(industry_symbol, bar_count, date, type_: str = "low"):
    return get_pct_rank(get_industry_stocks_(industry_symbol=industry_symbol, date=date), bar_count, date, type_)


def get_related_concept_pct_rank(stock_symbol: str, bar_count: int = 5, date: str = "now") -> Dict[str, pd.DataFrame]:
    """获取股票相关概念板块涨跌幅和排名

    Args:
        stock_symbol (str): 股票代码
        bar_count (int, optional): 数据长度. Defaults to 5.
        date (str, optional): 日期，默认为"now"

    Returns:
        Dict[str, pd.DataFrame]: key - 概念等级, value - index为概念名称，columns为'concept_pct', 'concept_rank', 'concept_count', 'stock_pct', 'stock_rank', 'stock_count'
        如果股票没有相关概念，则返回空字典
    """
    ret = {}
    tmp = pd.DataFrame(get_stock_related_concepts(stock_symbol, date=date))
    if tmp.empty:
        return ret
    tmp1 = dict(zip(tmp[0], tmp[2]))  # key: 概念板块代码，value: 概念板块中文名称
    for i, j in tmp.groupby(1):
        tmp2 = get_all_concept_pct_rank(bar_count, date)[i]
        tmp2 = tmp2.loc[tmp2.index.intersection(j[0])]
        tmp2 = tmp2.sort_values("concept_rank")
        for j_ in tmp2.index:
            tmp3 = get_concept_stocks_pct_rank(j_, bar_count, date)
            tmp2.loc[j_, "stock_pct"] = tmp3.loc[stock_symbol, "pct"]
            tmp2.loc[j_, "stock_rank"] = tmp3.loc[stock_symbol, "rank"]
            tmp2.loc[j_, "stock_count"] = tmp3.loc[stock_symbol, "count"]
            tmp2.loc[j_, "leading_stock"] = tmp3.index[np.argmax(tmp3["pct"])]
            tmp2.loc[j_, "leading_stock_pct"] = tmp3["pct"].iloc[np.argmax(tmp3["pct"])]
        tmp2["name"] = tmp2.index.map(lambda x: tmp1[x])
        ret[i] = tmp2
    return ret


def get_related_industries_pct_rank(stock_symbol: str, bar_count: int = 5, date: str = "now") -> pd.DataFrame:
    """获取股票相关行业板块涨跌幅和排名

    Args:
        stock_symbol (str): 股票代码
        bar_count (int, optional): 数据长度. Defaults to 5.
        date (str, optional): 日期，默认为"now"

    Returns:
        pd.DataFrame: index为industry_symbol，columns为'industry_pct', 'industry_rank', 'industry_count', 'stock_pct', 'stock_rank', 'stock_count'
        如果股票没有相关行业，则返回空DataFrame
    """
    tmp = pd.DataFrame(
        get_stock_related_industries(stock_symbol, date=date)
    )  # columns: 0 - 行业指数代码，1 - 行业分类标准，2 - 行业名称，3 - 行业板块代码
    if tmp.empty:
        return pd.DataFrame()
    ret = {}
    tmp = tmp.loc[~tmp[0].isna()]
    tmp1 = dict(zip(tmp[0], tmp[2]))  # key：行业指数代码，value：行业名称
    for i, j in tmp.groupby(1):
        tmp2: Dict[str, pd.DataFrame] = get_all_industries_pct_rank(bar_count, date)[i].loc[j[0]]
        for j1, j2 in zip(j[0], j[3]):
            tmp3: pd.DataFrame = get_industry_stocks_pct_rank(j2, bar_count, date)
            tmp2.loc[j1, "stock_pct"] = tmp3.loc[stock_symbol, "pct"]
            tmp2.loc[j1, "stock_rank"] = tmp3.loc[stock_symbol, "rank"]
            tmp2.loc[j1, "stock_count"] = tmp3.loc[stock_symbol, "count"]
            tmp2.loc[j1, "leading_stock"] = tmp3.index[np.argmax(tmp3["pct"])]
            tmp2.loc[j1, "leading_stock_pct"] = tmp3["pct"].iloc[np.argmax(tmp3["pct"])]
        tmp2["name"] = tmp2.index.map(lambda x: tmp1[x])
        tmp2["industry_type"] = i
        ret[i] = tmp2
    return pd.concat(ret.values(), axis=0)


def get_all_securities_(start, end) -> List:
    """获取指定日期范围内的所有股票代码"""
    ret = get_all_securities("stock", "nat")
    return ret.loc[(ret["start_date"] <= pd.Timestamp(end)) & (ret["end_date"] >= pd.Timestamp(start))].index.tolist()


class StockPoolMask:
    """股票池筛选器，用于剔除ST股、停牌股、北交所股票、上市时间小于250天的股票"""

    def __init__(self, n: int = 5, end_timestamp: Optional[int] = None):
        self.n = n
        self.cache_path = f"/home/<USER>/work/share/simu/yangfan/stock_pool_mask_{n}.pkl"
        self.lockfile = f"/home/<USER>/work/share/simu/yangfan/stock_pool_mask_{n}.lock"
        self.symbols = set(get_all_securities_("2010-01-01", "2030-01-01"))

        self.start: int = pd.to_datetime("2010-01-01").value
        self.st_df = pd.DataFrame()
        self.paused_df = pd.DataFrame()
        self.close_hit_limit_up_df = pd.DataFrame()
        self.close_hit_limit_dn_df = pd.DataFrame()
        self.open_hit_limit_up_df = pd.DataFrame()
        self.open_hit_limit_dn_df = pd.DataFrame()
        self.begin_time = {}

        if end_timestamp is None:
            now = pd.Timestamp.now(tz="Asia/Shanghai")
            # 先取最近的一个交易日，可能会是今天
            end_dt = get_latest_trade_day()
            # 如果取到的最新交易日是今天，那就把最新交易日向前推一天
            if now.date() == end_dt.date():
                end_dt = calc_trade_day(end_dt, -1)
            # 如果当前时间小于最新交易日第二天的5点，则再向前推一天，因为这个时候数据还没入库
            if now < end_dt.tz_localize("Asia/Shanghai") + pd.Timedelta(days=1, hours=5):
                end_dt = calc_trade_day(end_dt, -1)
            self.end = end_dt.replace(hour=15).tz_localize("Asia/Shanghai").value
        else:
            self.end = end_timestamp

        if pathlib.Path(self.cache_path).exists():
            with open(self.lockfile, "w") as lockfile:
                fcntl.flock(lockfile.fileno(), fcntl.LOCK_SH)
                with open(self.cache_path, "rb") as f:
                    data = pickle.load(f)
                fcntl.flock(lockfile.fileno(), fcntl.LOCK_UN)

            if (
                pd.to_datetime(self.end, utc=True).tz_convert("Asia/Shanghai")
                >= pd.to_datetime(data["end"], utc=True).tz_convert("Asia/Shanghai").normalize()
                + pd.Timedelta(days=1, hours=15)
            ) or set(
                [
                    "end",
                    "begin_time",
                    "st_df",
                    "paused_df",
                    "close_hit_limit_up_df",
                    "close_hit_limit_dn_df",
                    "open_hit_limit_up_df",
                    "open_hit_limit_dn_df",
                ]
            ).difference(set(data.keys())):
                self._get_st_and_paused_mask()
                self._get_begin_time()
                self._dump()
            elif self.end == data["end"]:
                self.st_df = data["st_df"]
                self.paused_df = data["paused_df"]
                self.close_hit_limit_up_df = data["close_hit_limit_up_df"]
                self.close_hit_limit_dn_df = data["close_hit_limit_dn_df"]
                self.open_hit_limit_up_df = data["open_hit_limit_up_df"]
                self.open_hit_limit_dn_df = data["open_hit_limit_dn_df"]
                self.begin_time = data["begin_time"]
            else:
                raise RuntimeError(f"{self.__class__.__name__} 数据有问题，end: {self.end}, 缓存end: {data['end']}")
        else:
            self._get_st_and_paused_mask()
            self._get_begin_time()
            self._dump()

    @lru_cache
    def _get_mask1(
        self,
        drop_paused,
        drop_close_hit_limit_up,
        drop_close_hit_limit_dn,
        drop_next_open_hit_limit_up,
        drop_next_open_hit_limit_dn,
    ):
        mask_df = self.st_df.copy()
        if drop_paused:
            if drop_paused == 1:
                paused_df = self.paused_df
            elif drop_paused == 2:
                paused_df = self.paused_df.rolling(
                    window=pd.api.indexers.FixedForwardWindowIndexer(window_size=self.n + 1), min_periods=1
                ).sum()  # 包含当日在内的未来n天内，如果出现停牌、未上市为True，否则为False
            else:
                raise ValueError("drop_paused should be 1 or 2")
            paused_df = (paused_df.fillna(1) > 0).astype(np.bool_)
            mask_df |= paused_df
        if drop_close_hit_limit_up:
            mask_df |= self.close_hit_limit_up_df
        if drop_close_hit_limit_dn:
            mask_df |= self.close_hit_limit_dn_df
        if drop_next_open_hit_limit_up:
            mask_df |= self.open_hit_limit_up_df.shift(-1).fillna(False)
        if drop_next_open_hit_limit_dn:
            mask_df |= self.open_hit_limit_dn_df.shift(-1).fillna(False)

        mask_df.index = (pd.to_datetime(mask_df.index) + pd.Timedelta(hours=7)).astype(int)
        mask_df = ~mask_df
        return mask_df

    def __call__(
        self,
        factor_df: pd.DataFrame,
        drop_paused: int = 1,
        drop_close_hit_limit_up: bool = False,
        drop_close_hit_limit_dn: bool = False,
        drop_next_open_hit_limit_up: bool = False,
        drop_next_open_hit_limit_dn: bool = False,
    ) -> pd.DataFrame:
        """筛选股票，默认一定剔除st

        Args:
            factor_df (pd.DataFrame): 待处理数据
            drop_paused (int, optional): 0 - 不剔除停牌，1 - 剔除当日停牌，2 - 剔除当日和未来N天停牌. Defaults to 1.
            drop_close_hit_limit_up (bool, optional): 剔除当天收盘涨停. Defaults to False.
            drop_close_hit_limit_dn (bool, optional): 剔除当天收盘跌停. Defaults to False.
            drop_next_open_hit_limit_up (bool, optional): 剔除次日开盘涨停. Defaults to False.
            drop_next_open_hit_limit_dn (bool, optional): 剔除次日开盘跌停. Defaults to False.

        Raises:
            RuntimeError: 如果待处理数据中包含的index超出时间范围
            RuntimeError: 如果待处理数据中包含的symbol没有上市时间

        Returns:
            pd.DataFrame: 处理后的数据
        """
        # 去掉北交所
        factor_df = factor_df.copy()
        drop_cols = [i for i in factor_df.columns if (not (i.endswith(".SH") or i.endswith(".SZ")) or i == "920002.SZ")]
        factor_df.drop(drop_cols, axis=1, inplace=True)

        if factor_df.columns.difference(list(self.symbols)).size > 0 or factor_df.index[0] < self.start:
            raise RuntimeError(f"{self.__class__.__name__} 数据不完整")
        if not (tmp := factor_df.columns.difference(self.begin_time)).empty:
            raise RuntimeError(f"{self.__class__.__name__} 上市时间数据不完整，缺少{tmp.tolist()}")

        if factor_df.index[-1] > self.end:
            factor_df = factor_df.loc[factor_df.index <= self.end]

        mask_df = self._get_mask1(
            drop_paused,
            drop_close_hit_limit_up,
            drop_close_hit_limit_dn,
            drop_next_open_hit_limit_up,
            drop_next_open_hit_limit_dn,
        )
        factor_df = factor_df[mask_df.reindex(index=factor_df.index, columns=factor_df.columns, fill_value=False)]

        # 去掉上市时间太短的股票
        tmp = pd.DataFrame(
            factor_df.index.values.reshape((-1, 1)).repeat(factor_df.shape[1], axis=1),
            index=factor_df.index,
            columns=factor_df.columns,
        ).ge(pd.Series(self.begin_time, index=factor_df.columns), axis=1)
        factor_df = factor_df[tmp]

        factor_df.dropna(how="all", axis=1, inplace=True)
        return factor_df

    def _get_st_and_paused_mask(self):
        """获取当日st、停牌(当天+未来n天)、开盘/收盘涨/跌停的mask"""
        start_dt = pd.to_datetime(self.start).strftime("%Y-%m-%d")
        end_dt = pd.to_datetime(self.end).strftime("%Y-%m-%d")
        tmp = get_price(
            list(self.symbols),
            start_dt,
            end_dt,
            "1d",
            ["is_st", "is_paused", "close", "high_limit", "low_limit", "open"],
            fq=None,
            is_panel=True,
            skip_paused=False,
        )
        st_df = tmp["is_st"].fillna(1).astype(np.bool_)  # st、停牌、未上市为True，否则为False
        self.st_df = st_df

        paused_df = tmp["is_paused"].fillna(1)
        # paused_df = paused_df.rolling(
        #     window=pd.api.indexers.FixedForwardWindowIndexer(window_size=self.n + 1), min_periods=1
        # ).sum()
        # paused_df = (paused_df.fillna(1) > 0).astype(
        #     np.bool_
        # )  # 包含当日在内的未来n天内，如果出现停牌、未上市为True，否则为False
        self.paused_df = paused_df

        self.close_hit_limit_up_df = (tmp["close"] >= tmp["high_limit"]).astype(np.bool_)
        self.close_hit_limit_dn_df = (tmp["close"] <= tmp["low_limit"]).astype(np.bool_)
        self.open_hit_limit_up_df = (tmp["open"] >= tmp["high_limit"]).astype(np.bool_)
        self.open_hit_limit_dn_df = (tmp["open"] <= tmp["low_limit"]).astype(np.bool_)

    def _get_begin_time(self):
        """获取上市时间，保存的数据为实际上市时间+250天"""
        for symbol in self.symbols:
            if symbol not in self.begin_time:
                self.begin_time[symbol] = (
                    get_security_info(symbol).start_date + datetime.timedelta(days=250, hours=7)
                ).timestamp() * 1e9

    def _dump(self):
        with open(self.lockfile, "w") as lockfile:
            fcntl.flock(lockfile.fileno(), fcntl.LOCK_EX)
            with open(self.cache_path, "wb") as f:
                pickle.dump(
                    {
                        "begin_time": self.begin_time,
                        "end": self.end,
                        "st_df": self.st_df,
                        "paused_df": self.paused_df,
                        "close_hit_limit_up_df": self.close_hit_limit_up_df,
                        "close_hit_limit_dn_df": self.close_hit_limit_dn_df,
                        "open_hit_limit_up_df": self.open_hit_limit_up_df,
                        "open_hit_limit_dn_df": self.open_hit_limit_dn_df,
                    },
                    f,
                )
            fcntl.flock(lockfile.fileno(), fcntl.LOCK_UN)


def get_thsindex(
    start_dt: str, end_dt: str, fields: str = "thsindex1", symbols: List[str] = [], dump: bool = True
) -> pd.DataFrame:
    """获取同花顺热度指数数据

    Args:
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        fields (str, optional): 字段. Defaults to "thsindex1".
        symbols (List[str], optional): 股票代码列表. Defaults to [].
        dump (bool, optional): 是否保存到缓存. Defaults to True.

    Returns:
        pd.DataFrame: 数据
    """
    start_dt_ = pd.Timestamp(f"{start_dt} 15:00:00", tz="Asia/Shanghai")
    end_dt_ = pd.Timestamp(f"{end_dt} 15:00:00", tz="Asia/Shanghai")

    cache_path = pathlib.Path("/home/<USER>/work/share/simu/yangfan/thsindex.h5")
    tdf = pd.DataFrame()
    if cache_path.exists():
        try:
            tdf = pd.read_hdf(cache_path, key=fields)
        except KeyError:
            pass
        except Exception as e:
            raise e
    symbols = symbols if len(symbols) > 0 else tdf.columns.tolist()

    cache_start_dt = np.inf if tdf.empty else tdf.index[0]
    cache_end_dt = -np.inf if tdf.empty else tdf.index[-1]
    cache_symbols = set(tdf.columns) if not tdf.empty else set()

    now = pd.Timestamp.now("Asia/Shanghai")
    if now.time() >= datetime.time(15, 0, 0):
        now = now.replace(hour=15, minute=0, second=0, microsecond=0)
    else:
        now = (now - datetime.timedelta(days=1)).replace(hour=15, minute=0, second=0, microsecond=0)

    start_dt_nstimestamp = to_nstimestamp(start_dt_)
    end_dt_nstimestamp = to_nstimestamp(end_dt_)

    if (
        start_dt_nstimestamp < cache_start_dt
        or end_dt_nstimestamp > cache_end_dt
        or set(symbols).difference(cache_symbols)
    ):
        open_api = get_open_api("share:research")
        tdf = open_api.get_hxfactor_thsindex(
            start_dt_.strftime("%Y-%m-%d"), end_dt_.strftime("%Y-%m-%d"), [fields], symbols
        )
        tdf = tdf.set_index(["date", "symbol"])[fields].unstack()
        tdf.index.rename(None, 1)
        tdf.columns.rename(None, 1)
        tdf.index = (pd.to_datetime(tdf.index) + pd.Timedelta(hours=7)).astype(int)
        tdf.sort_index(inplace=True)
        if dump:
            tdf.to_hdf(cache_path, key=fields, mode="w")

    return tdf[tdf.columns.intersection(symbols)].loc[
        (tdf.index >= start_dt_nstimestamp) & (tdf.index <= end_dt_nstimestamp)
    ]


def get_price_df(symbols, start_dt, end_dt, fre_step="1d", fields=["close"], fq="post", skip_paused=True):
    if symbols == "all":
        symbols = get_all_securities_(start_dt, end_dt)
    tmp = get_price(symbols, start_dt, end_dt, fre_step, fields, fq=fq, is_panel=True, skip_paused=skip_paused)
    ret = {}
    for field in fields:
        ret[field] = tmp[field]
        if fre_step[-1] == "d":
            ret[field].index = (pd.to_datetime(ret[field].index) + pd.Timedelta(hours=7)).astype(int)
        elif fre_step[-1] == "m":
            ret[field].index = (pd.to_datetime(ret[field].index) - pd.Timedelta(hours=8)).astype(int)
    return ret


def get_index_stocks_periodically(index_code: str, tmpl_df: pd.DataFrame):
    ret = pd.DataFrame(0, index=tmpl_df.index, columns=tmpl_df.columns)
    a = [f"{i}{j}" for i, j in product(range(2015, 2099), ["{:>02}01".format(k + 1) for k in range(0, 12, 3)])]
    for i_, j_ in zip(a[:-1], a[1:]):
        i = pd.Timestamp(i_).timestamp() * 1e9
        j = pd.Timestamp(j_).timestamp() * 1e9
        if i > ret.index[-1]:
            break
        aa = get_index_stocks(index_code, date=i_)
        ret.loc[(ret.index >= i) & (ret.index < j), ret.columns.intersection(aa)] = 1
    ret = ret.astype(bool)
    return ret
