import fcntl
import math
import pathlib
import shelve
from functools import lru_cache
from multiprocessing import Manager, RLock
from typing import Dict, List, Optional

import numpy as np
import pandas as pd
from loguru import logger

from aichemy.utils import is_notebook, slice_dataset, to_nstimestamp

from .utils import get_all_securities_, get_stock_related_industries, gl_all_trade_days, query_market_cap_table

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm
try:
    from mindgo_api import factor as smd_factor  # type: ignore
    from mindgo_api import (  # type: ignore
        get_all_securities,
        get_money_flow_step,
        get_open_api,
        get_price,
        get_trade_days,
        query,
        run_query,
    )
except ImportError:
    pass

HXFACTOR = [
    "量价强化学习类",
    "情绪强化学习类",
    "集合竞价量价类",
    "Tick快照量价类",
    "分钟频量价技术指标类",
    "日频量价技术指标类",
    "Tick快照深度模型类",
    "Transformer量价预测类",
    "新闻预测文本训练类",
    "逐笔成交频次量价类",
    "逐笔委托频次量价类",
]
TEST_HXFACTOR = ["000300.SH", "000852.SH", "000905.SH"]
EXCEPTED_FACTOR = {
    "量价强化学习类": ["RL_DAY_MF_ALPHA6", "RL_DAY_MF_ALPHA11", "RL_DAY_MF_ALPHA7"],
    "日频量价技术指标类": ["DAY_PV_VVR"],
    "集合竞价量价类": ["OB_BT_NBOC", "SS_RD_CA1R"],
    "新闻预测文本训练类": ["NEWS_BUZZ_IWF", "NEWS_BUZZ_ICQF"],
}
DEFAULT_FACTOR_CACHE_FILE = "/home/<USER>/work/share/simu/yangfan/factors.h5"
DEFAULT_FACTOR_LOCK = "/home/<USER>/work/share/simu/yangfan/factor.lock"
DEFAULT_INDUSTRY_DICT_CACHE_FILE = "/home/<USER>/work/share/simu/yangfan/industry_dict.db"


manager = Manager()
shared_dict = manager.dict()
mp_lock = RLock()


def encode_thscode(thscode: str) -> int:
    if thscode.endswith(".SH"):
        return int(f"1{thscode[:6]}")
    elif thscode.endswith(".SZ"):
        return int(f"2{thscode[:6]}")
    elif thscode.endswith(".BJ"):
        return int(f"3{thscode[:6]}")
    else:
        raise ValueError(f"thscode {thscode} 格式错误")


def decode_thscode(thscode: int) -> str:
    market = thscode // 1000000
    code = thscode % 1000000
    if market == 1:
        return f"{code:0>6}.SH"
    elif market == 2:
        return f"{code:0>6}.SZ"
    elif market == 3:
        return f"{code:0>6}.BJ"
    else:
        raise ValueError(f"thscode {thscode} 格式错误")


@lru_cache
def get_hxfactor_dict() -> Dict[str, List[str]]:
    """从数据库中获取因子字典，如{'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}"""
    factor_dict = {}
    open_api = get_open_api("share:research")
    for i in HXFACTOR:
        data = open_api.get_hxfactors_data(i, "now", "now", fields=None)
        factor_dict[i] = data.columns.difference(["time", "code"]).tolist()
    test = open_api.get_hxfactors_test()
    for i in TEST_HXFACTOR:
        factor_dict[i] = test.query(f'基准指数=="{i}"')["因子名称"].tolist()
    return factor_dict


@lru_cache
def get_factor_dict(factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> Dict[str, List[str]]:
    """获取目前已经保存到本地的因子字典

    Args:
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.

    Returns:
        Dict[str, List[str]]: {'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}
    """
    pathlib.Path(factor_cache_file).parent.mkdir(parents=True, exist_ok=True)
    ret = {}
    with open(DEFAULT_FACTOR_LOCK, "w") as lockfile:
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_SH)
        if pathlib.Path(factor_cache_file).exists():
            with pd.HDFStore(factor_cache_file, mode="r") as store:
                for key in store.keys():
                    t1, t2 = key[1:].split("-")  # 去掉开头的'/'
                    t2 = t2.replace("_orth", "")
                    if t1 not in ret:
                        ret[t1] = []
                    if t2 not in ret[t1]:
                        ret[t1].append(t2)
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_UN)
    return ret


def del_factor(name1: str, name2: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    """删除因子

    Args:
        name1 (str): 因子大类
        name2 (str): 因子名称
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
    """
    with open(DEFAULT_FACTOR_LOCK, "w") as lockfile:
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_EX)
        with pd.HDFStore(factor_cache_file, mode="a") as store:
            key = f"/{name1}-{name2}"
            if key in store:
                store.remove(key)  # type: ignore
            key_orth = f"/{name1}-{name2}_orth"
            if key_orth in store:
                store.remove(key_orth)  # type: ignore
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_UN)


def align_datetime_index(index: pd.DatetimeIndex) -> pd.DatetimeIndex:
    """要求index已经normalize"""
    if not index.difference(gl_all_trade_days).empty:
        raise RuntimeError(f"因子数据中包含非交易日数据，日期为：{index.difference(gl_all_trade_days)}")
    return gl_all_trade_days[(gl_all_trade_days >= index.min()) & (gl_all_trade_days <= index.max())].sort_values()


def dump_hxfactor(
    name1: str,
    name2: Optional[List],
    save_mode: int,
    start_dt: str,
    end_dt: str,
    chunk_size: int = 50,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
) -> None:
    """保存因子

    Args:
        name1 (str): 因子大类
        name2 (Optional[List]): 因子名称,None表示获取所有
        save_mode (int): 0 - 跳过已经保存的因子，只保存新的因子，1 - 所有因子全都重新保存，2 - 已经保存的因子做增量更新
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        chunk_size (int, optional): 分块大小. Defaults to 10.
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
    """
    all_hxf0 = get_hxfactor_dict().get(name1, [])  # 获取数据库里所有因子的列表
    all_hxf1 = get_factor_dict(factor_cache_file).get(name1, [])  # 获取本地缓存的因子列表

    if save_mode == 0:
        if name2 is None:
            hxf = [i for i in all_hxf0 if i not in all_hxf1]
        else:
            hxf = [i for i in name2 if i not in all_hxf1]
    elif save_mode == 1:
        if name2 is None:
            hxf = all_hxf0
        else:
            hxf = name2
    elif save_mode == 2:
        if name2 is None:
            hxf = all_hxf1
        else:
            hxf = [i for i in name2 if i in all_hxf1]

    n = math.ceil(len(hxf) / chunk_size)
    # 分块来取数据，不然数据量很大
    for i in tqdm(range(n), desc=f"{name1}"):
        # 从数据库中取数据
        open_api = get_open_api("share:research")
        if name1 in TEST_HXFACTOR:
            data = open_api.get_hxfactors_test(start_dt, end_dt, fields=hxf[i::n])
        else:
            data = open_api.get_hxfactors_data(name1, start_dt, end_dt, fields=hxf[i::n])
        data.set_index(["time", "code"], drop=True, append=False, inplace=True)
        columns = data.columns.to_list()
        for factor_name in columns:
            d: pd.DataFrame = data[factor_name].copy().unstack()
            d.index.rename(None, inplace=True)
            d.columns.rename(None, inplace=True)

            d.index = pd.to_datetime(d.index)
            if name1 == "集合竞价量价类":
                d = d.reindex(index=align_datetime_index(d.index))
                d.index = (d.index + pd.Timedelta(hours=1, minutes=25)).astype(int)
            else:
                d.index = gl_all_trade_days[
                    np.searchsorted(gl_all_trade_days.to_numpy(), d.index.to_numpy(), "left") - 1
                ]
                d = d.reindex(index=align_datetime_index(d.index))
                d.index = (d.index + pd.Timedelta(hours=7)).astype(int)

            key = f"/{name1}-{factor_name}"
            if save_mode == 0 or save_mode == 1:
                save_data_to_store(key, d.drop(["920002.SZ"], axis=1, errors="ignore"), factor_cache_file, mode="w")
            else:
                save_data_to_store(key, d.drop(["920002.SZ"], axis=1, errors="ignore"), factor_cache_file, mode="a")


def dump_market_cap_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    mct = query_market_cap_table().copy()
    mct.index = pd.to_datetime(mct.index)
    mct = mct.reindex(index=align_datetime_index(mct.index))
    mct.index = (mct.index + pd.Timedelta(hours=7)).astype(int)
    st = to_nstimestamp(f"{start_dt} 000000")
    ed = to_nstimestamp(f"{end_dt} 150000")
    mct = mct.loc[(mct.index >= st) & (mct.index <= ed)]
    save_data_to_store("/市值因子-市值因子", mct, factor_cache_file)


def dump_money_flow_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    fields = [
        "act_buy_xl",
        "pas_buy_xl",
        "act_buy_l",
        "pas_buy_l",
        "act_buy_m",
        "pas_buy_m",
        "act_sell_xl",
        "pas_sell_xl",
        "act_sell_l",
        "pas_sell_l",
        "act_sell_m",
        "pas_sell_m",
        "buy_l",
        "sell_l",
        "dde_l",
        "net_flow_rate",
        "l_net_value",
    ]
    data = get_money_flow_step(
        get_all_securities_(start_dt, end_dt),
        start_date=start_dt,
        end_date=end_dt,
        fre_step="1d",
        fields=fields,
        is_panel=True,
    )
    for i in fields:
        j = data[i].copy()
        j.index = pd.to_datetime(j.index)
        j = j.reindex(index=align_datetime_index(j.index))
        j.index = (j.index + pd.Timedelta(hours=7)).astype(int)
        save_data_to_store(f"/资金流-{i}", j, factor_cache_file)


def dump_industry_factor(
    start_dt: str,
    end_dt: str,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file: str = DEFAULT_INDUSTRY_DICT_CACHE_FILE,
) -> None:
    """保存行业因子

    Args:
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        industry_dict_cache_file (str, optional): 行业字典缓存文件. Defaults to DEFAULT_INDUSTRY_DICT_CACHE_FILE.
    """
    res = []
    for td in get_trade_days(start_dt, end_dt):  # type: ignore
        industry_dict = _get_industry_dict(td, industry_dict_cache_file)
        res.append(pd.DataFrame(industry_dict).loc["同花顺二级行业"].rename(td))
    res1: pd.DataFrame = pd.concat(res, axis=1).T  # index为日期，columns为股票代码，values为industry_index_thscode
    # index为nstimestamp，columns为股票代码
    res1.index = pd.to_datetime(res1.index)
    res1 = res1.reindex(index=align_datetime_index(res1.index))
    res1.index = (res1.index + pd.Timedelta(hours=7)).astype(int)

    fields = ["open", "close", "high", "low", "volume"]
    res3 = {field: pd.DataFrame(np.nan, index=res1.index, columns=res1.columns) for field in fields}

    for industry_index_thscode in [i for i in set(res1.values.flatten()) if isinstance(i, str)]:
        dddd = res1 == industry_index_thscode
        res2 = dddd.copy().astype(float)
        save_data_to_store(f"/行业因子-{industry_index_thscode}", res2, factor_cache_file)

        d = get_price(industry_index_thscode, start_dt, end_dt, "1d", fields, skip_paused=True, fq="post")
        d.index = (pd.to_datetime(d.index) + pd.Timedelta(hours=7)).astype(int)

        for field in fields:
            tmp = res3[field].copy()
            for stock_symbol in tmp.columns:
                tmp[stock_symbol] = d[field]
            res3[field][dddd] = tmp[dddd]

    for field in fields:
        save_data_to_store(f"/行业量价因子-{field}", res3[field], factor_cache_file)


def dump_financial_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    cols = """pe
pe_ttm
pb
pcf_cash_flow_ttm
ps
ps_ttm
dividend_rate
dividend_rate_12_months
market_cap
capitalization
current_market_cap
circulating_cap
weighted_roe
overall_assets_net_income_ratio
roa
net_profit_margin_on_sales
gross_sales_profits
ratio_of_sales_to_cost
net_profit_div_income
opt_profit_div_income
before_tax_profit_div_income
opt_cost_div_income
sale_cost_div_income
administration_cost_div_income
financing_cost_div_income
impairment_loss_div_income
basic_pey_ear_growth_ratio
diluted_peg_rowth_ratio
net_cashflow_psg_rowth_ratio
overall_income_growth_ratio
opt_income_growth_ratio
opt_profit_growth_ratio
total_profit_growth_ratio
net_profit_growth_ratio
parent_company_profit_growth_ratio
parent_company_share_holders_net_profit_growth_ratio
net_cashflow_from_opt_act_growth_ratio
diluted_net_asset_growth_ratio
cash_cycle
days_sales_of_inventory
days_sales_outstanding
days_payable_outstanding
turnover_days_of_current_assets
inventory_turnover_ratio
turnover_ratio_of_receivable
turnover_ratio_of_account_payable
turnover_of_current_assets
turnover_of_fixed_assets
turnover_of_overall_assets
current_ratio
quick_ratio
conservative_quick_ratio
equity_ratio
equity_liabilities_attributable_to_shareholders_of_parent_company
equity_int_liabilities_attributable_to_shareholders_of_parent_company
tangible_assets_liabilities
tangible_assets_int_liabilities
tangible_assets_net_liabilities
ebitda_liabilities
netcashflows_from_opt_act_int_liabilities
netcashflows_from_opt_act_net_liabilities
long_term_debt_to_opt_capital_ratio
net_debt_equity
int_debt_equity""".split("\n")

    for i in tqdm(range(5), desc="财务因子"):
        aaa = cols[i::5]
        data = run_query(
            query(
                smd_factor.symbol, smd_factor.date, *[getattr(smd_factor, j) for j in aaa if hasattr(smd_factor, j)]
            ).filter(smd_factor.date.between(f"{start_dt}", f"{end_dt}"))
        )
        data.columns = data.columns.str.replace(r"^factor_", "", regex=True)
        data.drop(["id"], axis=1, inplace=True, errors="ignore")
        data = data.set_index(["date", "symbol"])
        data.index.rename((None, None), inplace=True)
        for col in data.columns:
            tmp = data[col].copy().unstack()
            tmp.index = pd.to_datetime(tmp.index)
            tmp = tmp.reindex(index=align_datetime_index(tmp.index))
            tmp.index = (tmp.index + pd.Timedelta(hours=7)).astype(int)
            save_data_to_store(f"/财务因子-{col}", tmp, factor_cache_file)


def save_data_to_store(
    key: str, data: pd.DataFrame, store_file: str = DEFAULT_FACTOR_CACHE_FILE, mode: str = "a"
) -> None:
    data = data.copy().sort_index()

    # 数据中可能存在缺失值，处理方式是丢弃最后几行全是缺失值的数据，中间的保留，因为有些因子在9:10分更新，但是在这之后数据库已经有记录了
    tmp = ~data.isna().all(axis=1)
    finite_rows = np.where(tmp)[0]
    if finite_rows.size == 0:
        logger.info(f"{key} is empty")
        return
    else:
        data = data.iloc[: finite_rows.max() + 1]

    data.columns = data.columns.map(encode_thscode)
    with open(DEFAULT_FACTOR_LOCK, "w") as lockfile:
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_EX)
        with pd.HDFStore(store_file, mode="a") as store:
            if key in store:
                if mode == "a":
                    tmp = store.get(key)
                    if data.columns.isin(tmp.columns).all():
                        data = data.loc[~data.index.isin(tmp.index)].reindex(columns=tmp.columns)
                        store.append(key, data)
                    else:
                        store.remove(key)  # type: ignore
                        data = pd.concat([tmp.loc[~tmp.index.isin(data.index)], data], axis=0).sort_index()
                        store.put(key=key, value=data, format="table")
                else:
                    store.remove(key)  # type: ignore
                    store.put(key=key, value=data, format="table")
            else:
                store.put(key=key, value=data, format="table")
        if not data.empty:
            text = f"{key}保存完成, mode：{mode}, 时间：{pd.to_datetime(data.index.min(), utc=True).tz_convert('Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')} -\
{pd.to_datetime(data.index.max(), utc=True).tz_convert('Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')}"
            logger.info(text)
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_UN)
    get_factor_dict.cache_clear()


# def _orthogonalize_factor(factor_df: pd.DataFrame) -> pd.DataFrame:
#     """对因子df进行市值和行业因子正交化"""
#     x_list = []
#     for i in get_factor_dict()["行业因子"]:
#         x_list.append(get_factor("行业因子", i))
#     x_list.append(np.log(get_factor("市值因子", "市值因子")))
#     return orthogonalize_factor(factor_df, x_list)


def _get_industry_dict(dt, industry_dict_cache_file: str) -> Dict[str, Dict[str, str]]:
    """返回指定日期每个股票的行业字典，如{'000995.SZ': {'同花顺二级行业': '881133.TI', '申万二级行业': '801125.SL', '中信二级行业': 'CI005156.CI'}}"""
    # 2021年7月30日，同花顺二级行业分类调整，旧的行业代码映射不到指数代码，这里需要手动做映射
    old_industry_code_dict = {
        "T0203": "881107.TI",
        "T0602": "881116.TI",
        "T0704": "881120.TI",
        "T1001": "881129.TI",
        "T1902": "881156.TI",
        "T1903": "881157.TI",
        "T2201": "881162.TI",
        "T2202": "881163.TI",
    }
    dt = pd.Timestamp(dt).strftime("%Y-%m-%d")
    pathlib.Path(industry_dict_cache_file).parent.mkdir(parents=True, exist_ok=True)
    with shelve.open(industry_dict_cache_file) as db:
        # industry_dict - {'股票代码': {'同花顺二级行业': '881133.TI', '申万二级行业': '801125.SL', '中信二级行业': 'CI005156.CI'}}
        if dt in db:
            industry_dict = db[dt]
        else:
            industry_dict = {}
            for symbol in tqdm(get_all_securities("stock", dt).index.tolist(), desc=dt):
                try:
                    industry_dict[symbol] = dict(
                        [(i[1], i[0] if i[0] is not None else i[3]) for i in get_stock_related_industries(symbol, dt)]
                    )
                except Exception:
                    print(f"{dt} {symbol} 没有获取到相关行业")
            db[dt] = industry_dict

    for k in industry_dict.keys():
        if "同花顺二级行业" not in industry_dict[k]:
            print("{} {} 未获取到同花顺二级行业代码".format(dt, k))
            continue
        if industry_dict[k]["同花顺二级行业"] in old_industry_code_dict:
            industry_dict[k]["同花顺二级行业"] = old_industry_code_dict[industry_dict[k]["同花顺二级行业"]]
    return industry_dict


def get_factor(
    name1: str,
    name2: str,
    shift: int = 0,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file: str = DEFAULT_INDUSTRY_DICT_CACHE_FILE,
    start_dt: Optional[str] = None,
    end_dt: Optional[str] = None,
    force_save: bool = False,
    cached: bool = False,
) -> pd.DataFrame:
    """取因子数据

    Args:
        name1 (str): 因子大类名称
        name2 (str): 因子名称

        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        industry_dict_cache_file (str, optional): 行业字典缓存文件. Defaults to DEFAULT_INDUSTRY_DICT_CACHE_FILE.
        start_dt (Optional[str], optional): 开始日期，None则取到最早，为None时如果因子数据不存在则报错. Defaults to None.
        end_dt (Optional[str], optional): 结束日期，None则取到最后，为None时如果因子数据不存在则报错. Defaults to None.
        force_save (bool, optional): 是否强制保存. Defaults to False.

    Raises:
        ValueError: 当前缓存数据中没有所需的因子，需要传入start_dt 和 end_dt 用于提取数据

    Returns:
        pd.DataFrame: 因子数据
    """
    pathlib.Path(factor_cache_file).parent.mkdir(parents=True, exist_ok=True)

    factor_dict = get_factor_dict(factor_cache_file)
    if name1 not in factor_dict or name2 not in factor_dict[name1] or force_save:
        if start_dt is None or end_dt is None:
            raise ValueError("当前缓存数据中没有所需的因子，需要传入start_dt 和 end_dt 用于提取数据")

        if name1 in HXFACTOR:
            dump_hxfactor(name1, None, force_save, start_dt, end_dt, factor_cache_file=factor_cache_file)

            # for i in get_factor_dict()[name1]:
            #     db[f"{name1}-{i}_orth"] = _orthogonalize_factor(db[f"{name1}-{i}"])

        elif name1 in ["行业因子", "行业量价因子"]:
            dump_industry_factor(start_dt, end_dt, factor_cache_file, industry_dict_cache_file)

        elif name1 == "市值因子":
            dump_market_cap_factor(start_dt, end_dt, factor_cache_file)

    try:
        # with pd.HDFStore(factor_cache_file, mode="r") as store:
        #     key = f"/{name1}-{name2}"
        #     if key not in store:
        #         return pd.DataFrame()
        #     df = store[key].sort_index()
        df = _get_factor(factor_cache_file, name1, name2, cached).shift(shift)
        df = slice_dataset(df, start_dt and f"{start_dt} 000000", end_dt and f"{end_dt} 235959", "both")
        return df
    except FileNotFoundError:
        return pd.DataFrame()


def _get_factor(factor_cache_file: str, name1: str, name2: str, cached: bool) -> pd.DataFrame:
    if f"{name1}-{name2}" in shared_dict:
        return shared_dict[f"{name1}-{name2}"].copy()

    with open(DEFAULT_FACTOR_LOCK, "w") as lockfile:
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_SH)
        if not pathlib.Path(factor_cache_file).exists():
            return pd.DataFrame()
        with pd.HDFStore(factor_cache_file, mode="r") as store:
            key = f"/{name1}-{name2}"
            if key not in store:
                return pd.DataFrame()
            df = store[key].sort_index().copy()
            df.columns = df.columns.map(decode_thscode)
        fcntl.flock(lockfile.fileno(), fcntl.LOCK_UN)

    with mp_lock:
        if cached and f"{name1}-{name2}" not in shared_dict:
            shared_dict[f"{name1}-{name2}"] = df
    return df  # type: ignore
